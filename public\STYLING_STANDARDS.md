# 🎨 BDIO Registry - Styling Standards

## 📋 Current Styling Approach

### ✅ **Standardized Files (Using style.css):**
- `get.html` - Document detail page
- `get - Copy.html` - Document detail copy
- `index.html` - Dashboard (updated to use style.css)
- `register.html` - Document registration (updated to use style.css)

### 🎯 **Modern Files (Using Tailwind CSS):**
- `addversion.html` - Add document version (Tailwind + custom styles)

### 📝 **Other Files (Inline Styles):**
- `nft.html` - NFT management
- `withdraw.html` - Withdraw funds
- `endorse.html` - Document endorsement
- Various other utility pages

## 🎨 **Style.css Components Available**

### Layout Components
- `.container` - Main content container
- `.dashboard-container` - Dashboard specific container
- `.dashboard-header` - Dashboard header with gradient
- `.main-content` - Main content area
- `.glass-effect` - Glass morphism effect
- `.glass-card` - Glass card component

### Button Components
- `.btn` - Base button style
- `.btn-primary` - Primary button (blue gradient)
- `.btn-success` - Success button (green gradient)
- `.btn-outline` - Outline button
- `.btn-large` - Large button
- `.btn-small` - Small button

### Form Components
- `.form-group` - Form group container
- `.form-label` - Form label
- `.form-input` - Form input field
- `.form-textarea` - Textarea field
- `.form-select` - Select dropdown
- `.file-upload` - File upload component

### Status Components
- `.status-indicator` - Status badge
- `.status-active` - Active status (green)
- `.status-inactive` - Inactive status (red)
- `.status-pending` - Pending status (yellow)
- `.status-archived` - Archived status (gray)

### Table Components
- `.table-container` - Table wrapper
- `.table` - Table element
- `.bdio-id` - BDIO ID styling

### Alert Components
- `.alert` - Base alert
- `.alert-success` - Success alert
- `.alert-error` - Error alert
- `.alert-warning` - Warning alert
- `.alert-info` - Info alert

### Utility Classes
- `.text-center`, `.text-left`, `.text-right` - Text alignment
- `.mb-0` to `.mb-4` - Margin bottom
- `.mt-0` to `.mt-4` - Margin top
- `.p-0` to `.p-4` - Padding
- `.hidden`, `.block`, `.flex`, `.grid` - Display
- `.w-full`, `.h-full` - Width/height
- `.font-mono`, `.font-bold` - Typography
- `.opacity-50`, `.opacity-75` - Opacity

## 🚀 **Usage Guidelines**

### For New Pages
1. **Use style.css** as the primary stylesheet
2. **Add Tailwind CSS** if you need more utility classes
3. **Avoid inline styles** unless absolutely necessary

### For Existing Pages
1. **Keep working pages as-is** unless major updates needed
2. **Gradually migrate** to style.css when making updates
3. **Document any custom styles** needed

### Color Scheme
- **Primary**: #667eea to #764ba2 (blue-purple gradient)
- **Success**: #28a745 to #20c997 (green gradient)
- **Error**: #dc3545 (red)
- **Warning**: #ffc107 (yellow)
- **Info**: #17a2b8 (blue)

### Typography
- **Font Family**: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif
- **Monospace**: 'Courier New', monospace (for addresses, hashes)

## 📁 **File-Specific Notes**

### addversion.html
- Uses Tailwind CSS with custom glass effects
- Modern, professional design
- Keep as-is - it's the gold standard

### get.html & get - Copy.html
- Uses style.css extensively
- Good examples of the component system
- Document detail layout

### index.html
- Dashboard layout
- Uses dashboard-specific components
- Statistics grid and action cards

### register.html
- Form-heavy layout
- Uses form components from style.css
- File upload and validation

## 🔧 **Development Workflow**

1. **Check style.css first** - See if needed components exist
2. **Add to style.css** if new components needed
3. **Use utility classes** for simple styling
4. **Document new patterns** in this file

## 🎯 **Future Improvements**

1. **CSS Variables** - Add CSS custom properties for theming
2. **Dark Mode** - Add dark theme support
3. **Component Library** - Create reusable component documentation
4. **Build Process** - Add CSS optimization and purging

## 📚 **Resources**

- **Tailwind CSS**: https://tailwindcss.com/docs
- **Font Awesome**: https://fontawesome.com/icons
- **CSS Grid**: https://css-tricks.com/snippets/css/complete-guide-grid/
- **Flexbox**: https://css-tricks.com/snippets/css/a-guide-to-flexbox/

---

**Last Updated**: 2025-01-16
**Maintainer**: BDIO Development Team
