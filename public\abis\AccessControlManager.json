{"abi": [{"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "getApprovedSigners", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}], "name": "isSignerApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "isAdmin", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}], "name": "revokeS<PERSON>er", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "bdioId", "type": "string"}, {"indexed": true, "internalType": "address", "name": "signer", "type": "address"}], "name": "SignerApproved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "bdioId", "type": "string"}, {"indexed": true, "internalType": "address", "name": "signer", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}]}