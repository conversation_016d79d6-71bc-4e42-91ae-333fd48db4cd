{"abi": [{"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "verifyDocument", "outputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "bool", "name": "archived", "type": "bool"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "versionCount", "type": "uint256"}, {"internalType": "string", "name": "category", "type": "string"}, {"internalType": "string", "name": "metadataUri", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "getDocumentVersions", "outputs": [{"components": [{"internalType": "string", "name": "hashHex", "type": "string"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "string", "name": "note", "type": "string"}, {"internalType": "address", "name": "editor", "type": "address"}, {"internalType": "string", "name": "txHash", "type": "string"}], "internalType": "struct BDIOCoreRegistry.Version[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "getOwnershipHistory", "outputs": [{"components": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "string", "name": "note", "type": "string"}], "internalType": "struct BDIOCoreRegistry.OwnershipHistory[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "bdioToTokenId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "tokenURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "bdioId", "type": "string"}, {"indexed": true, "internalType": "address", "name": "old<PERSON>wner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}]}