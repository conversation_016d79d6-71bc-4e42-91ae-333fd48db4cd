{"abi": [{"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "getExpiry", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "isExpired", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "uint256", "name": "expiryTimestamp", "type": "uint256"}], "name": "setExpiry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string[]", "name": "bdioIds", "type": "string[]"}, {"internalType": "uint256[]", "name": "timestamps", "type": "uint256[]"}], "name": "batchSetExpiry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "clearExpiry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "ExpirySet", "type": "event"}]}