{"abi": [{"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "getEndorsementsCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getEndorsementByIndex", "outputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "string", "name": "note", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "string", "name": "note", "type": "string"}], "name": "signWithNote", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address[]", "name": "signers", "type": "address[]"}, {"internalType": "bytes[]", "name": "signatures", "type": "bytes[]"}, {"internalType": "string", "name": "note", "type": "string"}], "name": "batchEndorse", "outputs": [], "stateMutability": "payable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "bdioId", "type": "string"}, {"indexed": true, "internalType": "address", "name": "signer", "type": "address"}, {"internalType": "string", "name": "note", "type": "string"}], "name": "EndorsedWithNote", "type": "event"}]}