{"abi": [{"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "vcHash", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "string", "name": "newVCHash", "type": "string"}], "name": "setVCHash", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string[]", "name": "bdioIds", "type": "string[]"}, {"internalType": "string[]", "name": "vcHashes", "type": "string[]"}], "name": "batchSetVCHash", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "string", "name": "vcHash", "type": "string"}], "name": "VCHashSet", "type": "event"}]}