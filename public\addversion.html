<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>📄 Add Document Version - BDIO Registry</title>
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
  .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
  .glass-effect { backdrop-filter: blur(10px); background: rgba(255, 255, 255, 0.1); }
  .card-shadow { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
  .input-focus:focus { box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1); }
  .animate-pulse-slow { animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
  .status-indicator { width: 8px; height: 8px; border-radius: 50%; display: inline-block; margin-right: 8px; }
  .status-connected { background-color: #10b981; }
  .status-disconnected { background-color: #ef4444; }
  .status-loading { background-color: #f59e0b; animation: pulse 2s infinite; }
</style>
</head>
<body class="gradient-bg min-h-screen py-8">
<div class="container mx-auto px-4 max-w-2xl">
  <!-- Header -->
  <div class="text-center mb-8">
    <h1 class="text-4xl font-bold text-white mb-2">
      <i class="fas fa-file-plus mr-3"></i>Add Document Version
    </h1>
    <p class="text-indigo-100">Add a new version to your existing BDIO document</p>
  </div>

  <!-- Main Card -->
  <div class="glass-effect rounded-2xl p-8 card-shadow border border-white border-opacity-20">
    <!-- Connection Status -->
    <div class="mb-6 p-4 bg-white bg-opacity-10 rounded-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <span id="connectionStatus" class="status-indicator status-disconnected"></span>
          <span class="text-white font-medium">Wallet Status</span>
        </div>
        <span id="walletAddress" class="text-indigo-200 text-sm font-mono">Not connected</span>
      </div>
    </div>

    <!-- Form Section -->
    <div class="space-y-6">
      <!-- BDIO ID Input -->
      <div>
        <label class="block text-white font-medium mb-2">
          <i class="fas fa-fingerprint mr-2"></i>BDIO Document ID
        </label>
        <input
          id="bdioId"
          type="text"
          placeholder="Enter BDIO ID (e.g., 6fd49df8c)"
          class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-indigo-200 input-focus focus:outline-none focus:border-indigo-300 transition-all duration-200"
        />
      </div>

      <!-- File Upload Section -->
      <div>
        <label class="block text-white font-medium mb-2">
          <i class="fas fa-upload mr-2"></i>Upload New Document Version
        </label>
        <div class="relative">
          <input
            type="file"
            id="fileInput"
            class="hidden"
            accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
          />
          <button
            onclick="document.getElementById('fileInput').click()"
            class="w-full px-4 py-3 bg-white bg-opacity-20 border-2 border-dashed border-white border-opacity-30 rounded-lg text-white hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center"
          >
            <i class="fas fa-cloud-upload-alt mr-2"></i>
            <span id="fileLabel">Choose file to upload</span>
          </button>
        </div>
        <p class="text-indigo-200 text-sm mt-2">Supported formats: PDF, DOC, DOCX, TXT, JPG, PNG</p>
      </div>

      <!-- Hash Display -->
      <div>
        <label class="block text-white font-medium mb-2">
          <i class="fas fa-key mr-2"></i>Document Hash
        </label>
        <input
          id="newHash"
          type="text"
          placeholder="Hash will be generated automatically after file upload"
          readonly
          class="w-full px-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-30 rounded-lg text-indigo-200 font-mono text-sm"
        />
      </div>

      <!-- Version Note -->
      <div>
        <label class="block text-white font-medium mb-2">
          <i class="fas fa-sticky-note mr-2"></i>Version Note
        </label>
        <input
          id="note"
          type="text"
          placeholder="e.g., Version 2.0 - Updated content"
          class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-indigo-200 input-focus focus:outline-none focus:border-indigo-300 transition-all duration-200"
        />
      </div>

      <!-- Fee Information -->
      <div class="bg-yellow-500 bg-opacity-20 border border-yellow-400 border-opacity-30 rounded-lg p-4">
        <div class="flex items-center">
          <i class="fas fa-info-circle text-yellow-300 mr-2"></i>
          <span class="text-yellow-100 font-medium">Transaction Fee: <span class="fee-amount">0.2 POL</span></span>
        </div>
        <p class="text-yellow-200 text-sm mt-1">This fee is required to add a new version to the blockchain</p>
      </div>

      <!-- Action Button -->
      <button
        id="addVersionBtn"
        onclick="addVersion()"
        disabled
        class="w-full py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-bold rounded-lg hover:from-indigo-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 disabled:hover:scale-100"
      >
        <i class="fas fa-plus-circle mr-2"></i>
        <span id="btnText">Add Version</span>
      </button>
    </div>
  </div>

  <!-- Document Preview Section -->
  <div class="mt-8 glass-effect rounded-2xl p-6 card-shadow border border-white border-opacity-20" id="documentPreview" style="display: none;">
    <h3 class="text-xl font-bold text-white mb-4">
      <i class="fas fa-file-alt mr-2"></i>Document Information
    </h3>
    <div id="docInfo" class="space-y-3"></div>
  </div>

  <!-- Activity Log -->
  <div class="mt-8 glass-effect rounded-2xl p-6 card-shadow border border-white border-opacity-20">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-xl font-bold text-white">
        <i class="fas fa-history mr-2"></i>Activity Log
      </h3>
      <button onclick="clearLog()" class="text-indigo-300 hover:text-white text-sm">
        <i class="fas fa-trash mr-1"></i>Clear
      </button>
    </div>
    <div id="log" class="bg-black bg-opacity-30 rounded-lg p-4 h-64 overflow-y-auto font-mono text-sm text-green-300 whitespace-pre-wrap"></div>
  </div>

  <!-- Footer -->
  <div class="mt-8 text-center">
    <p class="text-indigo-200 text-sm">
      <i class="fas fa-shield-alt mr-1"></i>
      Powered by BDIO Registry - Secure Document Versioning on Blockchain
    </p>
    <div class="mt-2 space-x-4">
      <a href="get.html" class="text-indigo-300 hover:text-white text-sm">
        <i class="fas fa-search mr-1"></i>Verify Document
      </a>
      <a href="register.html" class="text-indigo-300 hover:text-white text-sm">
        <i class="fas fa-plus mr-1"></i>Register New Document
      </a>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<script src="js/app-config.js"></script>
<script>
// Contract configuration - will be loaded dynamically
let contractAddress = null;

// Complete ABI for the functions we need
const abi = [
  {
    "inputs": [
      {"internalType": "string", "name": "bdioId", "type": "string"},
      {"internalType": "string", "name": "newHash", "type": "string"},
      {"internalType": "string", "name": "note", "type": "string"},
      {"internalType": "string", "name": "txHash", "type": "string"}
    ],
    "name": "addVersion",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "verifyDocument",
    "outputs": [
      {"internalType": "address", "name": "owner", "type": "address"},
      {"internalType": "bool", "name": "active", "type": "bool"},
      {"internalType": "bool", "name": "archived", "type": "bool"},
      {"internalType": "uint256", "name": "timestamp", "type": "uint256"},
      {"internalType": "uint256", "name": "versionCount", "type": "uint256"},
      {"internalType": "string", "name": "category", "type": "string"},
      {"internalType": "string", "name": "metadataUri", "type": "string"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "getDocumentVersions",
    "outputs": [
      {
        "components": [
          {"internalType": "string", "name": "hashHex", "type": "string"},
          {"internalType": "uint256", "name": "timestamp", "type": "uint256"},
          {"internalType": "string", "name": "note", "type": "string"},
          {"internalType": "address", "name": "editor", "type": "address"},
          {"internalType": "string", "name": "txHash", "type": "string"}
        ],
        "internalType": "struct BDIOCoreRegistry.Version[]",
        "name": "",
        "type": "tuple[]"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "versionFee",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  }
];

// Global variables
let provider, signer, contract;
let currentVersionFee = BDIO_CONFIG.DEFAULT_FEES.VERSION_FEE; // Default fee in POL

// Initialize the application
async function init() {
  log('🚀 Initializing BDIO Version Manager...');

  // Load configuration first
  await initializeConfig();
  contractAddress = BDIO_CONFIG.CONTRACTS.BDIO_CORE_REGISTRY;

  if (!contractAddress) {
    log('❌ Contract address not available. Please check server configuration.');
    return;
  }

  log(`📋 Using contract: ${contractAddress}`);
  await connect();
  setupEventListeners();
}

// Connect to MetaMask wallet
async function connect() {
  try {
    if (!window.ethereum) {
      log('❌ MetaMask not detected. Please install MetaMask extension.');
      updateConnectionStatus(false, 'MetaMask not found');
      return false;
    }

    updateConnectionStatus('loading', 'Connecting...');
    log('🔗 Connecting to MetaMask...');

    provider = new ethers.BrowserProvider(window.ethereum);
    await provider.send("eth_requestAccounts", []);
    signer = await provider.getSigner();
    contract = new ethers.Contract(contractAddress, abi, signer);

    const address = await signer.getAddress();
    const network = await provider.getNetwork();

    log(`✅ Connected successfully!`);
    log(`👤 Wallet: ${address}`);
    log(`🌐 Network: ${network.name} (Chain ID: ${network.chainId})`);

    updateConnectionStatus(true, address);

    // Fetch current version fee
    try {
      const fee = await contract.versionFee();
      currentVersionFee = ethers.formatEther(fee);
      log(`💰 Current version fee: ${currentVersionFee} POL`);
      updateFeeDisplay(currentVersionFee);
    } catch (e) {
      log('⚠️ Could not fetch version fee, using default: 0.2 POL');
    }

    enableForm();
    return true;
  } catch (error) {
    console.error('Connection error:', error);
    log(`❌ Connection failed: ${error.message}`);
    updateConnectionStatus(false, 'Connection failed');
    return false;
  }
}

// Update connection status indicator
function updateConnectionStatus(connected, address = '') {
  const statusEl = document.getElementById('connectionStatus');
  const addressEl = document.getElementById('walletAddress');

  if (connected === 'loading') {
    statusEl.className = 'status-indicator status-loading';
    addressEl.textContent = address;
  } else if (connected) {
    statusEl.className = 'status-indicator status-connected';
    addressEl.textContent = `${address.slice(0, 6)}...${address.slice(-4)}`;
  } else {
    statusEl.className = 'status-indicator status-disconnected';
    addressEl.textContent = address || 'Not connected';
  }
}

// Update fee display
function updateFeeDisplay(fee) {
  const feeElements = document.querySelectorAll('.fee-amount');
  feeElements.forEach(el => el.textContent = `${fee} POL`);
}

// Enable form after successful connection
function enableForm() {
  document.getElementById('addVersionBtn').disabled = false;
}

// Setup event listeners
function setupEventListeners() {
  // File input change event
  document.getElementById('fileInput').addEventListener('change', handleFileUpload);

  // Form validation on input
  ['bdioId', 'note'].forEach(id => {
    document.getElementById(id).addEventListener('input', validateForm);
  });

  // BDIO ID input with document lookup
  document.getElementById('bdioId').addEventListener('blur', lookupDocument);
}

// Handle file upload and hash generation
async function handleFileUpload(event) {
  const file = event.target.files[0];
  const fileLabel = document.getElementById('fileLabel');

  if (!file) {
    fileLabel.textContent = 'Choose file to upload';
    document.getElementById('newHash').value = '';
    log('⚠️ No file selected');
    return;
  }

  // File size validation
  const maxSize = BDIO_CONFIG.SETTINGS.MAX_FILE_SIZE;
  if (file.size > maxSize) {
    log(`❌ File too large: ${formatFileSize(file.size)}. Maximum allowed: ${formatFileSize(maxSize)}`);
    fileLabel.innerHTML = `<i class="fas fa-exclamation-triangle text-red-400 mr-2"></i>File too large`;
    document.getElementById('newHash').value = '';
    return;
  }

  try {
    log(`📁 Processing file: ${file.name} (${formatFileSize(file.size)})`);
    fileLabel.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>Processing ${file.name}...`;

    // Show progress for large files
    if (file.size > 5 * 1024 * 1024) { // 5MB
      log('⏳ Large file detected, this may take a moment...');
    }

    // Generate hash from file
    const buffer = await file.arrayBuffer();
    const hash = ethers.keccak256(new Uint8Array(buffer));

    document.getElementById('newHash').value = hash;
    fileLabel.innerHTML = `<i class="fas fa-check text-green-400 mr-2"></i>${file.name}`;

    log(`🔑 File hash computed: ${hash}`);
    log(`📊 File details: ${file.name}, ${file.type || 'unknown'}, ${formatFileSize(file.size)}`);

    // Add copy button for hash
    const hashInput = document.getElementById('newHash');
    hashInput.title = 'Click to copy hash';
    hashInput.style.cursor = 'pointer';
    hashInput.onclick = async () => {
      if (await copyToClipboard(hash)) {
        log('📋 Hash copied to clipboard');
      }
    };

    validateForm();
  } catch (error) {
    console.error('File processing error:', error);
    log(`❌ Error processing file: ${error.message}`);
    fileLabel.innerHTML = `<i class="fas fa-exclamation-triangle text-red-400 mr-2"></i>Error processing file`;
    document.getElementById('newHash').value = '';
  }
}

// Validate form inputs
function validateForm() {
  const bdioId = document.getElementById('bdioId').value.trim();
  const newHash = document.getElementById('newHash').value.trim();
  const note = document.getElementById('note').value.trim();
  const btn = document.getElementById('addVersionBtn');

  // Validation rules
  const validations = {
    wallet: contract !== undefined,
    bdioId: bdioId.length >= BDIO_CONFIG.SETTINGS.MIN_BDIO_ID_LENGTH &&
            bdioId.length <= BDIO_CONFIG.SETTINGS.MAX_BDIO_ID_LENGTH,
    hash: newHash.length === BDIO_CONFIG.SETTINGS.HASH_LENGTH && newHash.startsWith('0x'),
    note: note.length >= 1 && note.length <= BDIO_CONFIG.SETTINGS.MAX_NOTE_LENGTH
  };

  const isValid = Object.values(validations).every(v => v);
  btn.disabled = !isValid;

  // Update button appearance
  if (isValid) {
    btn.classList.remove('opacity-50', 'cursor-not-allowed');
    btn.classList.add('hover:scale-105');
  } else {
    btn.classList.add('opacity-50', 'cursor-not-allowed');
    btn.classList.remove('hover:scale-105');
  }

  // Show validation hints
  if (bdioId && !validations.bdioId) {
    log('⚠️ BDIO ID must be between 10-64 characters');
  }
  if (note && !validations.note) {
    log('⚠️ Note must be between 1-256 characters');
  }
}

// Main function to add version
async function addVersion() {
  const bdioId = document.getElementById('bdioId').value.trim();
  const newHash = document.getElementById('newHash').value.trim();
  const note = document.getElementById('note').value.trim();
  const btn = document.getElementById('addVersionBtn');
  const btnText = document.getElementById('btnText');

  if (!bdioId || !newHash || !note) {
    log('⚠️ Please fill all required fields');
    return;
  }

  if (!contract) {
    log('❌ Wallet not connected. Please connect your wallet first.');
    return;
  }

  try {
    // Disable button and show loading state
    btn.disabled = true;
    btnText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';

    log('🔍 Verifying document ownership...');

    // Get current user address
    const myAddr = await signer.getAddress();

    // Verify document exists and get owner
    const docInfo = await contract.verifyDocument(bdioId);
    const owner = docInfo[0]; // owner is first element
    const active = docInfo[1];
    const archived = docInfo[2];
    const versionCount = docInfo[4];

    log(`👤 Your address: ${myAddr}`);
    log(`📄 Document owner: ${owner}`);
    log(`📊 Document status: ${active ? 'Active' : 'Inactive'}, ${archived ? 'Archived' : 'Not archived'}`);
    log(`📝 Current versions: ${versionCount}`);

    // Check ownership
    if (myAddr.toLowerCase() !== owner.toLowerCase()) {
      log('❌ Access denied: You are not the owner of this document.');
      return;
    }

    // Check if document is active
    if (!active) {
      log('❌ Cannot add version: Document is inactive.');
      return;
    }

    if (archived) {
      log('❌ Cannot add version: Document is archived.');
      return;
    }

    log('✅ Ownership verified. Preparing transaction...');

    // Calculate fee
    const feeWei = ethers.parseEther(currentVersionFee);
    log(`💰 Transaction fee: ${currentVersionFee} POL`);

    // Send transaction
    log('📡 Sending addVersion transaction...');
    btnText.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>Sending Transaction...';

    const tx = await contract.addVersion(bdioId, newHash, note, '', {
      value: feeWei
    });

    log(`⏳ Transaction sent: ${tx.hash}`);
    log('⏳ Waiting for confirmation...');
    btnText.innerHTML = '<i class="fas fa-clock mr-2"></i>Confirming...';

    const receipt = await tx.wait();

    log(`✅ Version added successfully!`);
    log(`📦 Block number: ${receipt.blockNumber}`);
    log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);
    log(`🔗 Transaction: ${tx.hash}`);

    // Reset form
    document.getElementById('bdioId').value = '';
    document.getElementById('newHash').value = '';
    document.getElementById('note').value = '';
    document.getElementById('fileInput').value = '';
    document.getElementById('fileLabel').textContent = 'Choose file to upload';

    log('🎉 Ready for next version!');

  } catch (error) {
    console.error('Transaction error:', error);

    let errorMessage = 'Unknown error occurred';
    if (error.reason) {
      errorMessage = error.reason;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.data?.message) {
      errorMessage = error.data.message;
    }

    log(`❌ Transaction failed: ${errorMessage}`);

    // Handle specific errors
    if (errorMessage.includes('insufficient funds')) {
      log('💡 Tip: Make sure you have enough POL for the transaction fee');
    } else if (errorMessage.includes('user rejected')) {
      log('💡 Transaction was cancelled by user');
    } else if (errorMessage.includes('Duplicate document hash')) {
      log('💡 This file hash already exists in the system');
    }

  } finally {
    // Re-enable button
    btn.disabled = false;
    btnText.innerHTML = '<i class="fas fa-plus-circle mr-2"></i>Add Version';
    validateForm();
  }
}

// Utility function for logging
function log(msg) {
  const el = document.getElementById('log');
  const timestamp = new Date().toLocaleTimeString();
  el.textContent += `[${timestamp}] ${msg}\n`;
  el.scrollTop = el.scrollHeight;
}

// Handle wallet account changes
if (window.ethereum) {
  window.ethereum.on('accountsChanged', (accounts) => {
    if (accounts.length === 0) {
      log('🔌 Wallet disconnected');
      updateConnectionStatus(false, 'Disconnected');
      document.getElementById('addVersionBtn').disabled = true;
    } else {
      log('🔄 Account changed, reconnecting...');
      connect();
    }
  });

  window.ethereum.on('chainChanged', (chainId) => {
    log(`🌐 Network changed to chain ID: ${chainId}`);
    log('🔄 Reconnecting...');
    window.location.reload();
  });
}

// Lookup document information when BDIO ID is entered
async function lookupDocument() {
  const bdioId = document.getElementById('bdioId').value.trim();
  const previewDiv = document.getElementById('documentPreview');
  const docInfoDiv = document.getElementById('docInfo');

  if (!bdioId || !contract) {
    previewDiv.style.display = 'none';
    return;
  }

  try {
    log(`🔍 Looking up document: ${bdioId}`);

    const docInfo = await contract.verifyDocument(bdioId);
    const owner = docInfo[0];
    const active = docInfo[1];
    const archived = docInfo[2];
    const timestamp = docInfo[3];
    const versionCount = docInfo[4];
    const category = docInfo[5];
    const metadataUri = docInfo[6];

    const date = new Date(Number(timestamp) * 1000).toLocaleString();
    const myAddr = await signer.getAddress();
    const isOwner = myAddr.toLowerCase() === owner.toLowerCase();

    docInfoDiv.innerHTML = `
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div class="bg-white bg-opacity-10 rounded-lg p-3">
          <div class="text-indigo-200 font-medium">Owner</div>
          <div class="text-white font-mono text-xs">${owner}</div>
          <div class="mt-1">
            ${isOwner ?
              '<span class="text-green-400"><i class="fas fa-check mr-1"></i>You own this document</span>' :
              '<span class="text-red-400"><i class="fas fa-times mr-1"></i>You do not own this document</span>'
            }
          </div>
        </div>
        <div class="bg-white bg-opacity-10 rounded-lg p-3">
          <div class="text-indigo-200 font-medium">Status</div>
          <div class="text-white">
            <span class="${active ? 'text-green-400' : 'text-red-400'}">
              <i class="fas fa-circle mr-1"></i>${active ? 'Active' : 'Inactive'}
            </span>
            ${archived ? '<span class="text-yellow-400 ml-2"><i class="fas fa-archive mr-1"></i>Archived</span>' : ''}
          </div>
        </div>
        <div class="bg-white bg-opacity-10 rounded-lg p-3">
          <div class="text-indigo-200 font-medium">Category</div>
          <div class="text-white">${category || 'Not specified'}</div>
        </div>
        <div class="bg-white bg-opacity-10 rounded-lg p-3">
          <div class="text-indigo-200 font-medium">Versions</div>
          <div class="text-white">${versionCount} version(s)</div>
        </div>
        <div class="bg-white bg-opacity-10 rounded-lg p-3">
          <div class="text-indigo-200 font-medium">Created</div>
          <div class="text-white text-xs">${date}</div>
        </div>
        <div class="bg-white bg-opacity-10 rounded-lg p-3">
          <div class="text-indigo-200 font-medium">Metadata URI</div>
          <div class="text-white text-xs break-all">${metadataUri || 'None'}</div>
        </div>
      </div>
    `;

    previewDiv.style.display = 'block';
    log(`✅ Document found: ${versionCount} versions, ${active ? 'active' : 'inactive'}`);

  } catch (error) {
    console.error('Document lookup error:', error);
    previewDiv.style.display = 'none';

    if (error.message.includes('execution reverted')) {
      log(`❌ Document not found: ${bdioId}`);
    } else {
      log(`❌ Error looking up document: ${error.message}`);
    }
  }
}

// Clear activity log
function clearLog() {
  document.getElementById('log').textContent = '';
  log('📝 Activity log cleared');
}

// Format file size
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Copy text to clipboard
async function copyToClipboard(text) {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    console.error('Failed to copy: ', err);
    return false;
  }
}

// Initialize when page loads
window.addEventListener('load', init);
</script>
</body>
</html>
