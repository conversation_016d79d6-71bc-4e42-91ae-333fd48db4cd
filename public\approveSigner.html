<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>🔑 Approve Signer - BDIO Registry</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<link rel="stylesheet" href="style.css">
</head>
<body>
<div class="container">
  <header class="header">
    <h1><i class="fas fa-user-shield"></i> Approve Signer</h1>
    <p class="subtitle">Manage document signers and access control</p>
  </header>


  <div class="main-content">
    <!-- Connection Status -->
    <div class="connection-status" id="connectionStatus">
      <span class="status-dot status-disconnected"></span>
      <span>Wallet Status: <span id="walletAddress">Not connected</span></span>
    </div>

    <!-- Form Section -->
    <div class="glass-card mb-4">
      <h3 class="section-title mb-3">
        <i class="fas fa-edit"></i> Signer Management
      </h3>

      <div class="form-group">
        <label class="form-label">
          <i class="fas fa-fingerprint"></i> BDIO Document ID *
        </label>
        <input
          type="text"
          id="bdioId"
          class="form-input"
          placeholder="Enter BDIO ID (e.g., abc123...)"
        />
      </div>

      <div class="form-group">
        <label class="form-label">
          <i class="fas fa-wallet"></i> Signer Address *
        </label>
        <input
          type="text"
          id="signerAddress"
          class="form-input font-mono"
          placeholder="Enter wallet address (e.g., 0x...)"
        />
      </div>

      <!-- Action Buttons -->
      <div class="action-grid">
        <div class="action-card">
          <h4><i class="fas fa-check-circle text-green-500"></i> Approve Signer</h4>
          <p>Grant signing permissions to an address</p>
          <button onclick="approveSigner()" class="btn btn-success">
            <i class="fas fa-user-plus"></i> Approve Signer
          </button>
        </div>

        <div class="action-card">
          <h4><i class="fas fa-times-circle text-red-500"></i> Revoke Signer</h4>
          <p>Remove signing permissions from an address</p>
          <button onclick="revokeSigner()" class="btn" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
            <i class="fas fa-user-minus"></i> Revoke Signer
          </button>
        </div>
      </div>

      <!-- Utility Buttons -->
      <div class="flex gap-3 mt-4">
        <button onclick="checkApproval()" class="btn btn-outline flex-1">
          <i class="fas fa-search"></i> Check Status
        </button>
        <button onclick="loadApprovedSigners()" class="btn btn-outline flex-1">
          <i class="fas fa-list"></i> Load All Signers
        </button>
      </div>
    </div>

    <!-- Status Section -->
    <div class="glass-card mb-4">
      <h3 class="section-title mb-3">
        <i class="fas fa-info-circle"></i> Status
      </h3>
      <div id="status" class="log-container" style="height: 150px; background: #f8f9fa; color: #333;">
        Ready to manage signers. Please connect your wallet to get started.
      </div>
    </div>

    <!-- Approved Signers List -->
    <div class="glass-card" id="signersSection" style="display: none;">
      <h3 class="section-title mb-3">
        <i class="fas fa-users"></i> Approved Signers
      </h3>
      <div class="signers-list" id="approvedSignersList"></div>
    </div>
  </div>
</div>

<script>
// Application Configuration
const APP_CONFIG = {
  MIN_BDIO_ID_LENGTH: 10,
  MAX_BDIO_ID_LENGTH: 64,
  DEFAULT_ENDORSEMENT_FEE: '0.15'
};

// Contract addresses - will be loaded from server
let accessControlAddress = null;
let bdioCoreRegistryAddress = null;

// Load contract configuration from server
async function loadContractConfig() {
  try {
    const response = await fetch('/api/config');
    if (response.ok) {
      const config = await response.json();
      accessControlAddress = config.accessControl;
      bdioCoreRegistryAddress = config.address;
      console.log('✅ Contract configuration loaded');
      return true;
    } else {
      throw new Error('Server config not available');
    }
  } catch (error) {
    console.warn('⚠️ Using fallback contract addresses');
    accessControlAddress = '0x1abfAE2B9b67B30EC092B0ca3D895495C6D32129';
    bdioCoreRegistryAddress = '0xC954fD7aC0cAeaD6BF54e5d9d8123b8D5E87FE6f';
    return false;
  }
}

// Complete ABI for the functions we need
const accessAbi = [
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}],
    "name": "approveSigner",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}],
    "name": "revokeSigner",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}],
    "name": "isSignerApproved",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "getApprovedSigners",
    "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}],
    "stateMutability": "view",
    "type": "function"
  }
];

const bdioAbi = [
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "verifyDocument",
    "outputs": [
      {"internalType": "address", "name": "owner", "type": "address"},
      {"internalType": "bool", "name": "active", "type": "bool"},
      {"internalType": "bool", "name": "archived", "type": "bool"},
      {"internalType": "uint256", "name": "timestamp", "type": "uint256"},
      {"internalType": "uint256", "name": "versionCount", "type": "uint256"},
      {"internalType": "string", "name": "category", "type": "string"},
      {"internalType": "string", "name": "metadataUri", "type": "string"}
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

let provider, signer, accessControl, bdioCore;

// Initialize the application
async function init() {
  log('🚀 Initializing Signer Management...');

  // Load contract configuration first
  await loadContractConfig();

  if (!accessControlAddress || !bdioCoreRegistryAddress) {
    log('❌ Contract addresses not available. Please check server configuration.');
    return;
  }

  if (!window.ethereum) {
    log('❌ MetaMask not detected. Please install MetaMask extension.');
    updateConnectionStatus(false, 'MetaMask not found');
    return;
  }

  try {
    updateConnectionStatus('loading', 'Connecting...');
    log('🔗 Connecting to MetaMask...');

    provider = new ethers.BrowserProvider(window.ethereum);
    await provider.send("eth_requestAccounts", []);
    signer = await provider.getSigner();

    accessControl = new ethers.Contract(accessControlAddress, accessAbi, signer);
    bdioCore = new ethers.Contract(bdioCoreRegistryAddress, bdioAbi, signer);

    const address = await signer.getAddress();
    const network = await provider.getNetwork();

    log(`✅ Connected successfully!`);
    log(`👤 Wallet: ${address}`);
    log(`🌐 Network: ${network.name} (Chain ID: ${network.chainId})`);
    log(`📋 AccessControl: ${accessControlAddress}`);
    log(`📋 BDIO Registry: ${bdioCoreRegistryAddress}`);

    updateConnectionStatus(true, address);

  } catch (error) {
    console.error('Connection error:', error);
    log(`❌ Connection failed: ${error.message}`);
    updateConnectionStatus(false, 'Connection failed');
  }
}

// Update connection status indicator
function updateConnectionStatus(connected, address = '') {
  const statusEl = document.querySelector('.status-dot');
  const addressEl = document.getElementById('walletAddress');

  if (connected === 'loading') {
    statusEl.className = 'status-dot status-loading';
    addressEl.textContent = address;
  } else if (connected) {
    statusEl.className = 'status-dot status-connected';
    addressEl.textContent = `${address.slice(0, 6)}...${address.slice(-4)}`;
  } else {
    statusEl.className = 'status-dot status-disconnected';
    addressEl.textContent = address || 'Not connected';
  }
}

async function approveSigner() {
  try {
    const bdioId = getInput('bdioId');
    const addr = getInput('signerAddress');

    if (!contract) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    log('🔍 Verifying document ownership...');

    // Verify document exists and get owner
    const docInfo = await bdioCore.verifyDocument(bdioId);
    const owner = docInfo[0]; // owner is first element
    const active = docInfo[1];
    const archived = docInfo[2];

    const myAddr = await signer.getAddress();

    log(`👤 Your address: ${myAddr}`);
    log(`📄 Document owner: ${owner}`);
    log(`📊 Document status: ${active ? 'Active' : 'Inactive'}, ${archived ? 'Archived' : 'Not archived'}`);

    // Check ownership
    if (myAddr.toLowerCase() !== owner.toLowerCase()) {
      log('❌ Access denied: You are not the owner of this document.');
      return;
    }

    // Check if document is active
    if (!active) {
      log('❌ Cannot approve signer: Document is inactive.');
      return;
    }

    if (archived) {
      log('❌ Cannot approve signer: Document is archived.');
      return;
    }

    // Check if signer is already approved
    const alreadyApproved = await accessControl.isSignerApproved(bdioId, addr);
    if (alreadyApproved) {
      log('⚠️ Signer is already approved for this document.');
      return;
    }

    log('✅ Ownership verified. Sending approval transaction...');
    log('📡 Sending approveSigner transaction...');

    const tx = await accessControl.approveSigner(bdioId, addr);
    log(`⏳ Transaction sent: ${tx.hash}`);
    log('⏳ Waiting for confirmation...');

    const receipt = await tx.wait();

    log(`✅ Signer approved successfully!`);
    log(`📦 Block number: ${receipt.blockNumber}`);
    log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);
    log(`🔗 Transaction: ${tx.hash}`);

    // Auto-refresh the signers list if it's visible
    const signersSection = document.getElementById('signersSection');
    if (signersSection.style.display !== 'none') {
      await loadApprovedSigners();
    }

  } catch (error) {
    console.error('Approve signer error:', error);

    let errorMessage = 'Unknown error occurred';
    if (error.reason) {
      errorMessage = error.reason;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.data?.message) {
      errorMessage = error.data.message;
    }

    log(`❌ Transaction failed: ${errorMessage}`);

    // Handle specific errors
    if (errorMessage.includes('insufficient funds')) {
      log('💡 Tip: Make sure you have enough POL for the transaction fee');
    } else if (errorMessage.includes('user rejected')) {
      log('💡 Transaction was cancelled by user');
    } else if (errorMessage.includes('Signer already approved')) {
      log('💡 This signer is already approved for this document');
    }
  }
}

async function revokeSigner() {
  try {
    const bdioId = getInput('bdioId');
    const addr = getInput('signerAddress');

    if (!accessControl) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    log('🔍 Verifying document ownership...');

    // Verify document exists and get owner
    const docInfo = await bdioCore.verifyDocument(bdioId);
    const owner = docInfo[0]; // owner is first element
    const active = docInfo[1];

    const myAddr = await signer.getAddress();

    log(`👤 Your address: ${myAddr}`);
    log(`📄 Document owner: ${owner}`);

    // Check ownership
    if (myAddr.toLowerCase() !== owner.toLowerCase()) {
      log('❌ Access denied: You are not the owner of this document.');
      return;
    }

    // Check if signer is currently approved
    const isApproved = await accessControl.isSignerApproved(bdioId, addr);
    if (!isApproved) {
      log('⚠️ Signer is not currently approved for this document.');
      return;
    }

    log('✅ Ownership verified. Sending revocation transaction...');
    log('📡 Sending revokeSigner transaction...');

    const tx = await accessControl.revokeSigner(bdioId, addr);
    log(`⏳ Transaction sent: ${tx.hash}`);
    log('⏳ Waiting for confirmation...');

    const receipt = await tx.wait();

    log(`✅ Signer revoked successfully!`);
    log(`📦 Block number: ${receipt.blockNumber}`);
    log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);
    log(`🔗 Transaction: ${tx.hash}`);

    // Auto-refresh the signers list if it's visible
    const signersSection = document.getElementById('signersSection');
    if (signersSection.style.display !== 'none') {
      await loadApprovedSigners();
    }

  } catch (error) {
    console.error('Revoke signer error:', error);

    let errorMessage = 'Unknown error occurred';
    if (error.reason) {
      errorMessage = error.reason;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.data?.message) {
      errorMessage = error.data.message;
    }

    log(`❌ Transaction failed: ${errorMessage}`);

    // Handle specific errors
    if (errorMessage.includes('insufficient funds')) {
      log('💡 Tip: Make sure you have enough POL for the transaction fee');
    } else if (errorMessage.includes('user rejected')) {
      log('💡 Transaction was cancelled by user');
    } else if (errorMessage.includes('Signer not approved')) {
      log('💡 This signer is not currently approved for this document');
    }
  }
}

async function checkApproval() {
  try {
    const bdioId = getInput('bdioId');
    const addr = getInput('signerAddress');

    if (!accessControl) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    log('🔍 Checking approval status...');

    // Verify document exists first
    try {
      const docInfo = await bdioCore.verifyDocument(bdioId);
      const owner = docInfo[0];
      const active = docInfo[1];
      const archived = docInfo[2];

      log(`📄 Document found - Owner: ${owner.slice(0, 6)}...${owner.slice(-4)}`);
      log(`📊 Status: ${active ? 'Active' : 'Inactive'}, ${archived ? 'Archived' : 'Not archived'}`);
    } catch (e) {
      log('❌ Document not found or invalid BDIO ID');
      return;
    }

    const approved = await accessControl.isSignerApproved(bdioId, addr);

    if (approved) {
      log(`✅ Signer ${addr.slice(0, 6)}...${addr.slice(-4)} is APPROVED for document ${bdioId}`);
    } else {
      log(`❌ Signer ${addr.slice(0, 6)}...${addr.slice(-4)} is NOT APPROVED for document ${bdioId}`);
    }

  } catch (error) {
    console.error('Check approval error:', error);
    log(`❌ Error checking approval: ${error.message}`);
  }
}

async function loadApprovedSigners() {
  try {
    const bdioId = getInput('bdioId');

    if (!accessControl) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    log('📋 Loading approved signers...');

    // Verify document exists first
    try {
      const docInfo = await bdioCore.verifyDocument(bdioId);
      const owner = docInfo[0];
      log(`📄 Document found - Owner: ${owner.slice(0, 6)}...${owner.slice(-4)}`);
    } catch (e) {
      log('❌ Document not found or invalid BDIO ID');
      return;
    }

    const signers = await accessControl.getApprovedSigners(bdioId);
    const signersSection = document.getElementById('signersSection');
    const list = document.getElementById('approvedSignersList');

    list.innerHTML = '';

    if (signers.length === 0) {
      list.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-users"></i>
          <h4>No Approved Signers</h4>
          <p>This document has no approved signers yet.</p>
        </div>
      `;
    } else {
      signers.forEach((addr, index) => {
        const signerCard = document.createElement('div');
        signerCard.className = 'glass-card mb-2 p-3';
        signerCard.innerHTML = `
          <div class="flex justify-between items-center">
            <div class="signer-info">
              <div class="signer-address mb-1">
                <i class="fas fa-user text-blue-500"></i>
                <span class="font-mono text-sm">${addr}</span>
              </div>
              <div class="signer-meta">
                <span class="status-indicator status-active">
                  <i class="fas fa-check"></i> Approved
                </span>
                <span class="text-sm opacity-75">Signer #${index + 1}</span>
              </div>
            </div>
            <button onclick="quickRevoke('${addr}')" class="btn btn-small" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
              <i class="fas fa-times"></i> Revoke
            </button>
          </div>
        `;
        list.appendChild(signerCard);
      });
    }

    signersSection.style.display = 'block';
    log(`✅ Found ${signers.length} approved signer(s) for document ${bdioId}`);

  } catch (error) {
    console.error('Load signers error:', error);
    log(`❌ Error loading signers: ${error.message}`);
  }
}

// Quick revoke function for the signer cards
async function quickRevoke(signerAddress) {
  const bdioId = getInput('bdioId');
  if (!bdioId) {
    log('❌ Please enter a BDIO ID first');
    return;
  }

  // Set the signer address and call revoke
  document.getElementById('signerAddress').value = signerAddress;
  await revokeSigner();
}

function getInput(id) {
  const val = document.getElementById(id).value.trim();
  if (!val) {
    log(`❌ ${id} is required`);
    throw new Error(`${id} is required`);
  }

  // Validate BDIO ID length
  if (id === 'bdioId' && (val.length < APP_CONFIG.MIN_BDIO_ID_LENGTH || val.length > APP_CONFIG.MAX_BDIO_ID_LENGTH)) {
    log(`❌ BDIO ID must be between ${APP_CONFIG.MIN_BDIO_ID_LENGTH}-${APP_CONFIG.MAX_BDIO_ID_LENGTH} characters`);
    throw new Error('Invalid BDIO ID length');
  }

  // Validate Ethereum address format
  if (id === 'signerAddress' && !ethers.isAddress(val)) {
    log(`❌ Invalid Ethereum address format`);
    throw new Error('Invalid address format');
  }

  return val;
}

function log(msg) {
  const el = document.getElementById('status');
  const timestamp = new Date().toLocaleTimeString();
  el.textContent += `[${timestamp}] ${msg}\n`;
  el.scrollTop = el.scrollHeight;
}

// Handle wallet account changes
if (window.ethereum) {
  window.ethereum.on('accountsChanged', (accounts) => {
    if (accounts.length === 0) {
      log('🔌 Wallet disconnected');
      updateConnectionStatus(false, 'Disconnected');
    } else {
      log('🔄 Account changed, reconnecting...');
      init();
    }
  });

  window.ethereum.on('chainChanged', (chainId) => {
    log(`🌐 Network changed to chain ID: ${chainId}`);
    log('🔄 Reconnecting...');
    window.location.reload();
  });
}

// Add some CSS for the signer cards
const style = document.createElement('style');
style.textContent = `
  .signer-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  }

  .signer-info {
    flex: 1;
  }

  .signer-address {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
  }

  .signer-meta {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .justify-between {
    justify-content: space-between;
  }

  .items-center {
    align-items: center;
  }

  .text-blue-500 {
    color: #3b82f6;
  }

  .text-gray-500 {
    color: #6b7280;
  }
`;
document.head.appendChild(style);

// Initialize when page loads
window.addEventListener('load', init);
</script>

</body>
</html>
