<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>🔑 Approve Signer - BDIO Registry</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<link rel="stylesheet" href="style.css">
<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
  }

  .container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
  }

  .header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 300;
  }

  .header p {
    opacity: 0.9;
    font-size: 1.1rem;
  }

  .form-container {
    padding: 40px;
  }

  .form-group {
    margin-bottom: 25px;
  }

  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
  }

  .required {
    color: #e74c3c;
  }

  .form-control {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
  }

  .form-control:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  .btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 18px 30px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
  }

  .btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }

  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .btn-success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  }

  .btn-success:hover:not(:disabled) {
    box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
  }

  .btn-danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  }

  .btn-danger:hover:not(:disabled) {
    box-shadow: 0 10px 20px rgba(231, 76, 60, 0.3);
  }

  .btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
  }

  .btn-outline:hover:not(:disabled) {
    background: #667eea;
    color: white;
  }

  .wallet-info {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
    border: 1px solid #4caf50;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .wallet-info i {
    color: #4caf50;
  }

  .status-container {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
  }

  .status-title {
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
  }

  .status-content {
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #666;
    line-height: 1.5;
    max-height: 200px;
    overflow-y: auto;
  }

  .action-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 25px;
  }

  .signers-list {
    margin-top: 20px;
  }

  .signer-card {
    background: #f8f9fa;
    border: 1px solid #e1e8ed;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .signer-info {
    flex: 1;
  }

  .signer-address {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #333;
    margin-bottom: 5px;
  }

  .signer-meta {
    font-size: 0.8rem;
    color: #666;
  }

  .btn-small {
    padding: 8px 15px;
    font-size: 0.9rem;
    width: auto;
    margin: 0;
  }

  @media (max-width: 768px) {
    .container {
      margin: 10px;
      border-radius: 15px;
    }

    .form-container {
      padding: 20px;
    }

    .header {
      padding: 20px;
    }

    .header h1 {
      font-size: 2rem;
    }

    .action-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
</head>
<body>
<div class="container">
  <div class="header">
    <h1><i class="fas fa-user-shield"></i> Approve Signer</h1>
    <p>Manage document signers and access control</p>
  </div>

  <div class="form-container">
    <!-- Wallet Connection Status -->
    <div id="walletInfo" class="wallet-info">
      <i class="fas fa-wallet"></i> <strong>Wallet Connected:</strong> <span id="walletAddress">Not connected</span>
    </div>

    <!-- Form Section -->
    <div class="form-group">
      <label for="bdioId">BDIO Document ID <span class="required">*</span></label>
      <input
        type="text"
        id="bdioId"
        class="form-control"
        placeholder="Enter BDIO ID (e.g., abc123...)"
      />
    </div>

    <div class="form-group">
      <label for="signerAddress">Signer Address <span class="required">*</span></label>
      <input
        type="text"
        id="signerAddress"
        class="form-control"
        placeholder="Enter wallet address (e.g., 0x...)"
        style="font-family: 'Courier New', monospace;"
      />
    </div>

    <!-- Action Buttons -->
    <div class="action-grid">
      <button onclick="approveSigner()" class="btn btn-success">
        <i class="fas fa-user-plus"></i> Approve Signer
      </button>

      <button onclick="revokeSigner()" class="btn btn-danger">
        <i class="fas fa-user-minus"></i> Revoke Signer
      </button>
    </div>

    <!-- Utility Buttons -->
    <button onclick="checkApproval()" class="btn btn-outline">
      <i class="fas fa-search"></i> Check Approval Status
    </button>

    <button onclick="loadApprovedSigners()" class="btn btn-outline">
      <i class="fas fa-list"></i> Load All Approved Signers
    </button>

    <!-- Status Section -->
    <div class="status-container">
      <div class="status-title">
        <i class="fas fa-info-circle"></i> Activity Log
      </div>
      <div id="status" class="status-content">
        Ready to manage signers. Please connect your wallet to get started.
      </div>
    </div>

    <!-- Approved Signers List -->
    <div id="signersSection" style="display: none;">
      <h3 style="margin: 30px 0 15px 0; color: #333;">
        <i class="fas fa-users"></i> Approved Signers
      </h3>
      <div class="signers-list" id="approvedSignersList"></div>
    </div>
  </div>
</div>

<script>
// Application Configuration
const APP_CONFIG = {
  MIN_BDIO_ID_LENGTH: 10,
  MAX_BDIO_ID_LENGTH: 64,
  DEFAULT_ENDORSEMENT_FEE: '0.15'
};

// Contract addresses - will be loaded from server
let accessControlAddress = null;
let bdioCoreRegistryAddress = null;

// Load contract configuration from server
async function loadContractConfig() {
  try {
    const response = await fetch('/api/config');
    if (response.ok) {
      const config = await response.json();
      accessControlAddress = config.accessControl;
      bdioCoreRegistryAddress = config.address;
      console.log('✅ Contract configuration loaded');
      return true;
    } else {
      throw new Error('Server config not available');
    }
  } catch (error) {
    console.warn('⚠️ Using fallback contract addresses');
    accessControlAddress = '0x1abfAE2B9b67B30EC092B0ca3D895495C6D32129';
    bdioCoreRegistryAddress = '0xC954fD7aC0cAeaD6BF54e5d9d8123b8D5E87FE6f';
    return false;
  }
}

// Complete ABI for the functions we need
const accessAbi = [
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}],
    "name": "approveSigner",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}],
    "name": "revokeSigner",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}],
    "name": "isSignerApproved",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "getApprovedSigners",
    "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}],
    "stateMutability": "view",
    "type": "function"
  }
];

const bdioAbi = [
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "verifyDocument",
    "outputs": [
      {"internalType": "address", "name": "owner", "type": "address"},
      {"internalType": "bool", "name": "active", "type": "bool"},
      {"internalType": "bool", "name": "archived", "type": "bool"},
      {"internalType": "uint256", "name": "timestamp", "type": "uint256"},
      {"internalType": "uint256", "name": "versionCount", "type": "uint256"},
      {"internalType": "string", "name": "category", "type": "string"},
      {"internalType": "string", "name": "metadataUri", "type": "string"}
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

let provider, signer, accessControl, bdioCore;

// Initialize the application
async function init() {
  log('🚀 Initializing Signer Management...');

  // Load contract configuration first
  await loadContractConfig();

  if (!accessControlAddress || !bdioCoreRegistryAddress) {
    log('❌ Contract addresses not available. Please check server configuration.');
    return;
  }

  if (!window.ethereum) {
    log('❌ MetaMask not detected. Please install MetaMask extension.');
    updateConnectionStatus(false, 'MetaMask not found');
    return;
  }

  try {
    updateConnectionStatus('loading', 'Connecting...');
    log('🔗 Connecting to MetaMask...');

    provider = new ethers.BrowserProvider(window.ethereum);
    await provider.send("eth_requestAccounts", []);
    signer = await provider.getSigner();

    accessControl = new ethers.Contract(accessControlAddress, accessAbi, signer);
    bdioCore = new ethers.Contract(bdioCoreRegistryAddress, bdioAbi, signer);

    const address = await signer.getAddress();
    const network = await provider.getNetwork();

    log(`✅ Connected successfully!`);
    log(`👤 Wallet: ${address}`);
    log(`🌐 Network: ${network.name} (Chain ID: ${network.chainId})`);
    log(`📋 AccessControl: ${accessControlAddress}`);
    log(`📋 BDIO Registry: ${bdioCoreRegistryAddress}`);

    updateConnectionStatus(true, address);

  } catch (error) {
    console.error('Connection error:', error);
    log(`❌ Connection failed: ${error.message}`);
    updateConnectionStatus(false, 'Connection failed');
  }
}

// Update connection status indicator
function updateConnectionStatus(connected, address = '') {
  const walletInfo = document.getElementById('walletInfo');
  const addressEl = document.getElementById('walletAddress');

  if (connected === 'loading') {
    addressEl.textContent = 'Connecting...';
    walletInfo.style.background = 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)';
    walletInfo.style.borderColor = '#ffc107';
  } else if (connected) {
    addressEl.textContent = `${address.slice(0, 6)}...${address.slice(-4)}`;
    walletInfo.style.background = 'linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%)';
    walletInfo.style.borderColor = '#4caf50';
  } else {
    addressEl.textContent = address || 'Not connected';
    walletInfo.style.background = 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)';
    walletInfo.style.borderColor = '#dc3545';
  }
}

async function approveSigner() {
  try {
    const bdioId = getInput('bdioId');
    const addr = getInput('signerAddress');

    if (!accessControl) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    log('🔍 Verifying document ownership...');

    // Verify document exists and get owner
    const docInfo = await bdioCore.verifyDocument(bdioId);
    const owner = docInfo[0]; // owner is first element
    const active = docInfo[1];
    const archived = docInfo[2];

    const myAddr = await signer.getAddress();

    log(`👤 Your address: ${myAddr}`);
    log(`📄 Document owner: ${owner}`);
    log(`📊 Document status: ${active ? 'Active' : 'Inactive'}, ${archived ? 'Archived' : 'Not archived'}`);

    // Check ownership
    if (myAddr.toLowerCase() !== owner.toLowerCase()) {
      log('❌ Access denied: You are not the owner of this document.');
      return;
    }

    // Check if document is active
    if (!active) {
      log('❌ Cannot approve signer: Document is inactive.');
      return;
    }

    if (archived) {
      log('❌ Cannot approve signer: Document is archived.');
      return;
    }

    // Check if signer is already approved
    const alreadyApproved = await accessControl.isSignerApproved(bdioId, addr);
    if (alreadyApproved) {
      log('⚠️ Signer is already approved for this document.');
      return;
    }

    log('✅ Ownership verified. Sending approval transaction...');
    log('📡 Sending approveSigner transaction...');

    const tx = await accessControl.approveSigner(bdioId, addr);
    log(`⏳ Transaction sent: ${tx.hash}`);
    log('⏳ Waiting for confirmation...');

    const receipt = await tx.wait();

    log(`✅ Signer approved successfully!`);
    log(`📦 Block number: ${receipt.blockNumber}`);
    log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);
    log(`🔗 Transaction: ${tx.hash}`);

    // Auto-refresh the signers list if it's visible
    const signersSection = document.getElementById('signersSection');
    if (signersSection.style.display !== 'none') {
      await loadApprovedSigners();
    }

  } catch (error) {
    console.error('Approve signer error:', error);

    let errorMessage = 'Unknown error occurred';
    if (error.reason) {
      errorMessage = error.reason;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.data?.message) {
      errorMessage = error.data.message;
    }

    log(`❌ Transaction failed: ${errorMessage}`);

    // Handle specific errors
    if (errorMessage.includes('insufficient funds')) {
      log('💡 Tip: Make sure you have enough POL for the transaction fee');
    } else if (errorMessage.includes('user rejected')) {
      log('💡 Transaction was cancelled by user');
    } else if (errorMessage.includes('Signer already approved')) {
      log('💡 This signer is already approved for this document');
    }
  }
}

async function revokeSigner() {
  try {
    const bdioId = getInput('bdioId');
    const addr = getInput('signerAddress');

    if (!accessControl) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    log('🔍 Verifying document ownership...');

    // Verify document exists and get owner
    const docInfo = await bdioCore.verifyDocument(bdioId);
    const owner = docInfo[0]; // owner is first element
    const active = docInfo[1];

    const myAddr = await signer.getAddress();

    log(`👤 Your address: ${myAddr}`);
    log(`📄 Document owner: ${owner}`);

    // Check ownership
    if (myAddr.toLowerCase() !== owner.toLowerCase()) {
      log('❌ Access denied: You are not the owner of this document.');
      return;
    }

    // Check if signer is currently approved
    const isApproved = await accessControl.isSignerApproved(bdioId, addr);
    if (!isApproved) {
      log('⚠️ Signer is not currently approved for this document.');
      return;
    }

    log('✅ Ownership verified. Sending revocation transaction...');
    log('📡 Sending revokeSigner transaction...');

    const tx = await accessControl.revokeSigner(bdioId, addr);
    log(`⏳ Transaction sent: ${tx.hash}`);
    log('⏳ Waiting for confirmation...');

    const receipt = await tx.wait();

    log(`✅ Signer revoked successfully!`);
    log(`📦 Block number: ${receipt.blockNumber}`);
    log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);
    log(`🔗 Transaction: ${tx.hash}`);

    // Auto-refresh the signers list if it's visible
    const signersSection = document.getElementById('signersSection');
    if (signersSection.style.display !== 'none') {
      await loadApprovedSigners();
    }

  } catch (error) {
    console.error('Revoke signer error:', error);

    let errorMessage = 'Unknown error occurred';
    if (error.reason) {
      errorMessage = error.reason;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.data?.message) {
      errorMessage = error.data.message;
    }

    log(`❌ Transaction failed: ${errorMessage}`);

    // Handle specific errors
    if (errorMessage.includes('insufficient funds')) {
      log('💡 Tip: Make sure you have enough POL for the transaction fee');
    } else if (errorMessage.includes('user rejected')) {
      log('💡 Transaction was cancelled by user');
    } else if (errorMessage.includes('Signer not approved')) {
      log('💡 This signer is not currently approved for this document');
    }
  }
}

async function checkApproval() {
  try {
    const bdioId = getInput('bdioId');
    const addr = getInput('signerAddress');

    if (!accessControl) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    log('🔍 Checking approval status...');

    // Verify document exists first
    try {
      const docInfo = await bdioCore.verifyDocument(bdioId);
      const owner = docInfo[0];
      const active = docInfo[1];
      const archived = docInfo[2];

      log(`📄 Document found - Owner: ${owner.slice(0, 6)}...${owner.slice(-4)}`);
      log(`📊 Status: ${active ? 'Active' : 'Inactive'}, ${archived ? 'Archived' : 'Not archived'}`);
    } catch (e) {
      log('❌ Document not found or invalid BDIO ID');
      return;
    }

    const approved = await accessControl.isSignerApproved(bdioId, addr);

    if (approved) {
      log(`✅ Signer ${addr.slice(0, 6)}...${addr.slice(-4)} is APPROVED for document ${bdioId}`);
    } else {
      log(`❌ Signer ${addr.slice(0, 6)}...${addr.slice(-4)} is NOT APPROVED for document ${bdioId}`);
    }

  } catch (error) {
    console.error('Check approval error:', error);
    log(`❌ Error checking approval: ${error.message}`);
  }
}

async function loadApprovedSigners() {
  try {
    const bdioId = getInput('bdioId');

    if (!accessControl) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    log('📋 Loading approved signers...');

    // Verify document exists first
    try {
      const docInfo = await bdioCore.verifyDocument(bdioId);
      const owner = docInfo[0];
      log(`📄 Document found - Owner: ${owner.slice(0, 6)}...${owner.slice(-4)}`);
    } catch (e) {
      log('❌ Document not found or invalid BDIO ID');
      return;
    }

    const signers = await accessControl.getApprovedSigners(bdioId);
    const signersSection = document.getElementById('signersSection');
    const list = document.getElementById('approvedSignersList');

    list.innerHTML = '';

    if (signers.length === 0) {
      list.innerHTML = `
        <div style="text-align: center; padding: 40px; color: #666;">
          <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;"></i>
          <h4 style="margin-bottom: 10px;">No Approved Signers</h4>
          <p>This document has no approved signers yet.</p>
        </div>
      `;
    } else {
      signers.forEach((addr, index) => {
        const signerCard = document.createElement('div');
        signerCard.className = 'signer-card';
        signerCard.innerHTML = `
          <div class="signer-info">
            <div class="signer-address">
              <i class="fas fa-user" style="color: #667eea; margin-right: 8px;"></i>
              ${addr}
            </div>
            <div class="signer-meta">
              <span style="color: #27ae60; font-weight: 600;">
                <i class="fas fa-check"></i> Approved
              </span>
              <span>Signer #${index + 1}</span>
            </div>
          </div>
          <button onclick="quickRevoke('${addr}')" class="btn btn-small btn-danger">
            <i class="fas fa-times"></i> Revoke
          </button>
        `;
        list.appendChild(signerCard);
      });
    }

    signersSection.style.display = 'block';
    log(`✅ Found ${signers.length} approved signer(s) for document ${bdioId}`);

  } catch (error) {
    console.error('Load signers error:', error);
    log(`❌ Error loading signers: ${error.message}`);
  }
}

// Quick revoke function for the signer cards
async function quickRevoke(signerAddress) {
  const bdioId = getInput('bdioId');
  if (!bdioId) {
    log('❌ Please enter a BDIO ID first');
    return;
  }

  // Set the signer address and call revoke
  document.getElementById('signerAddress').value = signerAddress;
  await revokeSigner();
}

function getInput(id) {
  const val = document.getElementById(id).value.trim();
  if (!val) {
    log(`❌ ${id} is required`);
    throw new Error(`${id} is required`);
  }

  // Validate BDIO ID length
  if (id === 'bdioId' && (val.length < APP_CONFIG.MIN_BDIO_ID_LENGTH || val.length > APP_CONFIG.MAX_BDIO_ID_LENGTH)) {
    log(`❌ BDIO ID must be between ${APP_CONFIG.MIN_BDIO_ID_LENGTH}-${APP_CONFIG.MAX_BDIO_ID_LENGTH} characters`);
    throw new Error('Invalid BDIO ID length');
  }

  // Validate Ethereum address format
  if (id === 'signerAddress' && !ethers.isAddress(val)) {
    log(`❌ Invalid Ethereum address format`);
    throw new Error('Invalid address format');
  }

  return val;
}

function log(msg) {
  const el = document.getElementById('status');
  const timestamp = new Date().toLocaleTimeString();
  el.textContent += `[${timestamp}] ${msg}\n`;
  el.scrollTop = el.scrollHeight;
}

// Handle wallet account changes
if (window.ethereum) {
  window.ethereum.on('accountsChanged', (accounts) => {
    if (accounts.length === 0) {
      log('🔌 Wallet disconnected');
      updateConnectionStatus(false, 'Disconnected');
    } else {
      log('🔄 Account changed, reconnecting...');
      init();
    }
  });

  window.ethereum.on('chainChanged', (chainId) => {
    log(`🌐 Network changed to chain ID: ${chainId}`);
    log('🔄 Reconnecting...');
    window.location.reload();
  });
}

// No additional CSS needed - using inline styles consistent with register.html

// Initialize when page loads
window.addEventListener('load', init);
</script>

</body>
</html>
