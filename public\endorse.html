<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>✍️ Endorse Document - BDIO Registry</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<link rel="stylesheet" href="style.css">
<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
  }

  .container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
  }

  .header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 300;
  }

  .header p {
    opacity: 0.9;
    font-size: 1.1rem;
  }

  .form-container {
    padding: 40px;
  }

  .form-group {
    margin-bottom: 25px;
  }

  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
  }

  .required {
    color: #e74c3c;
  }

  .form-control {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
  }

  .form-control:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  textarea.form-control {
    min-height: 100px;
    resize: vertical;
  }

  .btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 18px 30px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
  }

  .btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }

  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .btn-success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  }

  .btn-success:hover:not(:disabled) {
    box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
  }

  .btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
  }

  .btn-outline:hover:not(:disabled) {
    background: #667eea;
    color: white;
  }

  .wallet-info {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
    border: 1px solid #4caf50;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .wallet-info i {
    color: #4caf50;
  }

  .fee-info {
    background: #fff3cd;
    border: 1px solid #ffc107;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .fee-info i {
    color: #856404;
  }

  .status-container {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
  }

  .status-title {
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
  }

  .status-content {
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #666;
    line-height: 1.5;
    max-height: 200px;
    overflow-y: auto;
  }

  .endorsements-list {
    margin-top: 20px;
  }

  .endorsement-card {
    background: #f8f9fa;
    border: 1px solid #e1e8ed;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
  }

  .endorsement-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e1e8ed;
  }

  .endorsement-signer {
    flex: 1;
  }

  .endorsement-address {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #333;
    margin-bottom: 5px;
  }

  .endorsement-meta {
    font-size: 0.8rem;
    color: #666;
  }

  .endorsement-note {
    background: #e9ecef;
    padding: 10px;
    border-radius: 5px;
    font-style: italic;
    color: #495057;
    margin-top: 10px;
  }

  @media (max-width: 768px) {
    .container {
      margin: 10px;
      border-radius: 15px;
    }

    .form-container {
      padding: 20px;
    }

    .header {
      padding: 20px;
    }

    .header h1 {
      font-size: 2rem;
    }
  }
</style>
</head>
<body>
<div class="container">
  <div class="header">
    <h1><i class="fas fa-signature"></i> Document Endorsement</h1>
    <p>Sign and endorse documents with blockchain verification</p>
  </div>

  <div class="form-container">
    <!-- Wallet Connection Status -->
    <div id="walletInfo" class="wallet-info">
      <i class="fas fa-wallet"></i> <strong>Wallet Connected:</strong> <span id="walletAddress">Not connected</span>
    </div>

    <!-- Form Section -->
    <div class="form-group">
      <label for="bdioId">BDIO Document ID <span class="required">*</span></label>
      <input
        type="text"
        id="bdioId"
        class="form-control"
        placeholder="Enter BDIO ID (e.g., abc123...)"
      />
    </div>

    <div class="form-group">
      <label for="note">Endorsement Note (Optional)</label>
      <textarea
        id="note"
        class="form-control"
        rows="4"
        placeholder="Add your endorsement note or comments..."
      ></textarea>
      <small style="color: #666; margin-top: 5px; display: block;">
        This note will be permanently stored on the blockchain
      </small>
    </div>

    <!-- Fee Information -->
    <div class="fee-info">
      <i class="fas fa-info-circle"></i>
      <strong>Endorsement Fee:</strong> <span id="feeDisplay">Loading...</span>
    </div>

    <!-- Action Button -->
    <button onclick="endorse()" class="btn btn-success" id="signBtn" disabled>
      <i class="fas fa-signature"></i> Sign & Endorse Document
    </button>

    <!-- Utility Button -->
    <button onclick="loadEndorsements()" class="btn btn-outline">
      <i class="fas fa-list"></i> Load All Endorsements
    </button>

    <!-- Status Section -->
    <div class="status-container">
      <div class="status-title">
        <i class="fas fa-info-circle"></i> Activity Log
      </div>
      <div id="status" class="status-content">
        Ready to endorse documents. Please connect your wallet to get started.
      </div>
    </div>

    <!-- Endorsements List -->
    <div id="endorsementsSection" style="display: none;">
      <h3 style="margin: 30px 0 15px 0; color: #333;">
        <i class="fas fa-users"></i> Document Endorsements
      </h3>
      <div class="endorsements-list" id="endorsementList"></div>
    </div>
  </div>
</div>

<script>
// Application Configuration
const APP_CONFIG = {
  MIN_BDIO_ID_LENGTH: 10,
  MAX_BDIO_ID_LENGTH: 64,
  MAX_NOTE_LENGTH: 500,
  DEFAULT_ENDORSEMENT_FEE: '0.15'
};

// Contract addresses - loaded from server configuration
let endorsementAddress = null;
let accessControlAddress = null;
let bdioCoreAddress = null;

// Load contract configuration
async function loadConfig() {
  try {
    const response = await fetch('/api/config');
    if (response.ok) {
      const config = await response.json();
      endorsementAddress = config.endorsement;
      accessControlAddress = config.accessControl;
      bdioCoreAddress = config.address;
      console.log('✅ Contract configuration loaded');
      return true;
    }
  } catch (error) {
    console.warn('⚠️ Using fallback addresses:', error.message);
    // Fallback addresses
    endorsementAddress = '0xd87a5d71bb96E10f057B3E15464F1bEe5Bf71ECf';
    accessControlAddress = '0x1abfAE2B9b67B30EC092B0ca3D895495C6D32129';
    bdioCoreAddress = '0xC954fD7aC0cAeaD6BF54e5d9d8123b8D5E87FE6f';
    return false;
  }
}

let provider, signer, endorsementContract, accessControlContract, bdioContract;
let currentEndorsementFee = APP_CONFIG.DEFAULT_ENDORSEMENT_FEE;

// Complete ABI for the functions we need
const endorsementAbi = [
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "string", "name": "note", "type": "string"}],
    "name": "signWithNote",
    "outputs": [],
    "stateMutability": "payable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "getEndorsementsCount",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "uint256", "name": "index", "type": "uint256"}],
    "name": "getEndorsementByIndex",
    "outputs": [
      {"internalType": "address", "name": "signer", "type": "address"},
      {"internalType": "string", "name": "note", "type": "string"},
      {"internalType": "uint256", "name": "timestamp", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "endorsementFee",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  }
];

const accessControlAbi = [
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}],
    "name": "isSignerApproved",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "view",
    "type": "function"
  }
];

const bdioAbi = [
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "verifyDocument",
    "outputs": [
      {"internalType": "address", "name": "owner", "type": "address"},
      {"internalType": "bool", "name": "active", "type": "bool"},
      {"internalType": "bool", "name": "archived", "type": "bool"},
      {"internalType": "uint256", "name": "timestamp", "type": "uint256"},
      {"internalType": "uint256", "name": "versionCount", "type": "uint256"},
      {"internalType": "string", "name": "category", "type": "string"},
      {"internalType": "string", "name": "metadataUri", "type": "string"}
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

// Initialize the application
async function init() {
  log('🚀 Initializing Document Endorsement...');

  // Load contract configuration first
  await loadConfig();

  if (!endorsementAddress || !accessControlAddress || !bdioCoreAddress) {
    log('❌ Contract addresses not available. Please check server configuration.');
    return;
  }

  if (!window.ethereum) {
    log('❌ MetaMask not detected. Please install MetaMask extension.');
    updateConnectionStatus(false, 'MetaMask not found');
    return;
  }

  try {
    updateConnectionStatus('loading', 'Connecting...');
    log('🔗 Connecting to MetaMask...');

    provider = new ethers.BrowserProvider(window.ethereum);
    await provider.send("eth_requestAccounts", []);
    signer = await provider.getSigner();

    endorsementContract = new ethers.Contract(endorsementAddress, endorsementAbi, signer);
    accessControlContract = new ethers.Contract(accessControlAddress, accessControlAbi, signer);
    bdioContract = new ethers.Contract(bdioCoreAddress, bdioAbi, signer);

    const address = await signer.getAddress();
    const network = await provider.getNetwork();

    log(`✅ Connected successfully!`);
    log(`👤 Wallet: ${address}`);
    log(`🌐 Network: ${network.name} (Chain ID: ${network.chainId})`);
    log(`📋 Endorsement: ${endorsementAddress}`);
    log(`📋 AccessControl: ${accessControlAddress}`);
    log(`📋 BDIO Registry: ${bdioCoreAddress}`);

    updateConnectionStatus(true, address);

    // Load endorsement fee
    try {
      const fee = await endorsementContract.endorsementFee();
      currentEndorsementFee = ethers.formatEther(fee);
      document.getElementById('feeDisplay').textContent = `${currentEndorsementFee} POL`;
      log(`💰 Endorsement fee: ${currentEndorsementFee} POL`);
    } catch (e) {
      console.warn("Could not fetch endorsement fee from contract, using default");
      document.getElementById('feeDisplay').textContent = `${APP_CONFIG.DEFAULT_ENDORSEMENT_FEE} POL (fallback)`;
      log(`💰 Using fallback endorsement fee: ${APP_CONFIG.DEFAULT_ENDORSEMENT_FEE} POL`);
    }

    enableForm();

  } catch (error) {
    console.error('Connection error:', error);
    log(`❌ Connection failed: ${error.message}`);
    updateConnectionStatus(false, 'Connection failed');
  }
}

// Update connection status indicator
function updateConnectionStatus(connected, address = '') {
  const walletInfo = document.getElementById('walletInfo');
  const addressEl = document.getElementById('walletAddress');

  if (connected === 'loading') {
    addressEl.textContent = 'Connecting...';
    walletInfo.style.background = 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)';
    walletInfo.style.borderColor = '#ffc107';
  } else if (connected) {
    addressEl.textContent = `${address.slice(0, 6)}...${address.slice(-4)}`;
    walletInfo.style.background = 'linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%)';
    walletInfo.style.borderColor = '#4caf50';
  } else {
    addressEl.textContent = address || 'Not connected';
    walletInfo.style.background = 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)';
    walletInfo.style.borderColor = '#dc3545';
  }
}

// Enable form after successful connection
function enableForm() {
  document.getElementById('signBtn').disabled = false;
}

async function endorse() {
  try {
    const bdioId = getInput('bdioId');
    const note = document.getElementById('note').value.trim() || '';

    if (!endorsementContract) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    log('🔍 Verifying document and permissions...');

    // Verify document exists and get info
    const docInfo = await bdioContract.verifyDocument(bdioId);
    const owner = docInfo[0];
    const active = docInfo[1];
    const archived = docInfo[2];

    log(`📄 Document found - Owner: ${owner.slice(0, 6)}...${owner.slice(-4)}`);
    log(`📊 Status: ${active ? 'Active' : 'Inactive'}, ${archived ? 'Archived' : 'Not archived'}`);

    // Check if document is active
    if (!active) {
      log('❌ Cannot endorse: Document is inactive.');
      return;
    }

    if (archived) {
      log('❌ Cannot endorse: Document is archived.');
      return;
    }

    // Check if user is approved signer
    const myAddr = await signer.getAddress();
    log(`👤 Checking approval for: ${myAddr}`);

    const approved = await accessControlContract.isSignerApproved(bdioId, myAddr);
    if (!approved) {
      log('❌ Access denied: You are not an approved signer for this document.');
      log('💡 Tip: Ask the document owner to approve you as a signer first.');
      return;
    }

    log('✅ Approval verified. Preparing endorsement...');

    // Calculate fee
    const feeWei = ethers.parseEther(currentEndorsementFee);
    log(`💰 Endorsement fee: ${currentEndorsementFee} POL`);

    // Send endorsement transaction
    log('📡 Sending endorsement transaction...');

    const tx = await endorsementContract.signWithNote(bdioId, note, {
      value: feeWei
    });

    log(`⏳ Transaction sent: ${tx.hash}`);
    log('⏳ Waiting for confirmation...');

    const receipt = await tx.wait();

    log(`✅ Document endorsed successfully!`);
    log(`📦 Block number: ${receipt.blockNumber}`);
    log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);
    log(`🔗 Transaction: ${tx.hash}`);

    if (note) {
      log(`📝 Note: "${note}"`);
    }

    // Clear form
    document.getElementById('bdioId').value = '';
    document.getElementById('note').value = '';

    log('🎉 Ready for next endorsement!');

    // Auto-load endorsements
    await loadEndorsements();

  } catch (error) {
    console.error('Endorsement error:', error);

    let errorMessage = 'Unknown error occurred';
    if (error.reason) {
      errorMessage = error.reason;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.data?.message) {
      errorMessage = error.data.message;
    }

    log(`❌ Endorsement failed: ${errorMessage}`);

    // Handle specific errors
    if (errorMessage.includes('insufficient funds')) {
      log('💡 Tip: Make sure you have enough POL for the endorsement fee');
    } else if (errorMessage.includes('user rejected')) {
      log('💡 Transaction was cancelled by user');
    } else if (errorMessage.includes('Signer not approved')) {
      log('💡 You need to be approved as a signer for this document first');
    } else if (errorMessage.includes('Document not found')) {
      log('💡 Please check the BDIO ID and try again');
    }
  }
}

async function loadEndorsements() {
  try {
    const bdioId = getInput('bdioId');

    if (!endorsementContract) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    log('📡 Loading endorsements...');

    // Verify document exists first
    try {
      const docInfo = await bdioContract.verifyDocument(bdioId);
      const owner = docInfo[0];
      log(`📄 Document found - Owner: ${owner.slice(0, 6)}...${owner.slice(-4)}`);
    } catch (e) {
      log('❌ Document not found or invalid BDIO ID');
      return;
    }

    const count = await endorsementContract.getEndorsementsCount(bdioId);
    const endorsementsSection = document.getElementById('endorsementsSection');
    const list = document.getElementById('endorsementList');

    list.innerHTML = '';

    if (count == 0) {
      list.innerHTML = `
        <div style="text-align: center; padding: 40px; color: #666;">
          <i class="fas fa-signature" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;"></i>
          <h4 style="margin-bottom: 10px;">No Endorsements</h4>
          <p>This document has no endorsements yet.</p>
        </div>
      `;
    } else {
      for (let i = 0; i < count; i++) {
        const endorsement = await endorsementContract.getEndorsementByIndex(bdioId, i);
        const signerAddr = endorsement[0];
        const note = endorsement[1];
        const timestamp = endorsement[2];

        const date = new Date(Number(timestamp) * 1000);

        const endorsementCard = document.createElement('div');
        endorsementCard.className = 'endorsement-card';
        endorsementCard.innerHTML = `
          <div class="endorsement-header">
            <div class="endorsement-signer">
              <div class="endorsement-address">
                <i class="fas fa-user" style="color: #667eea; margin-right: 8px;"></i>
                ${signerAddr}
              </div>
              <div class="endorsement-meta">
                <span style="color: #27ae60; font-weight: 600;">
                  <i class="fas fa-check"></i> Endorsed
                </span>
                <span>${date.toLocaleString()}</span>
              </div>
            </div>
            <span style="font-size: 0.8rem; color: #666;">#${i + 1}</span>
          </div>
          ${note ? `
            <div class="endorsement-note">
              <strong>Note:</strong> "${note}"
            </div>
          ` : ''}
        `;
        list.appendChild(endorsementCard);
      }
    }

    endorsementsSection.style.display = 'block';
    log(`✅ Found ${count} endorsement(s) for document ${bdioId}`);

  } catch (error) {
    console.error('Load endorsements error:', error);
    log(`❌ Error loading endorsements: ${error.message}`);
  }
}

function getInput(id) {
  const val = document.getElementById(id).value.trim();
  if (!val) {
    log(`❌ ${id} is required`);
    throw new Error(`${id} is required`);
  }

  // Validate BDIO ID length
  if (id === 'bdioId' && (val.length < APP_CONFIG.MIN_BDIO_ID_LENGTH || val.length > APP_CONFIG.MAX_BDIO_ID_LENGTH)) {
    log(`❌ BDIO ID must be between ${APP_CONFIG.MIN_BDIO_ID_LENGTH}-${APP_CONFIG.MAX_BDIO_ID_LENGTH} characters`);
    throw new Error('Invalid BDIO ID length');
  }

  // Validate note length
  if (id === 'note' && val.length > APP_CONFIG.MAX_NOTE_LENGTH) {
    log(`❌ Note must be less than ${APP_CONFIG.MAX_NOTE_LENGTH} characters`);
    throw new Error('Note too long');
  }

  return val;
}

function log(msg) {
  const el = document.getElementById('status');
  const timestamp = new Date().toLocaleTimeString();
  el.textContent += `[${timestamp}] ${msg}\n`;
  el.scrollTop = el.scrollHeight;
}

// Handle wallet account changes
if (window.ethereum) {
  window.ethereum.on('accountsChanged', (accounts) => {
    if (accounts.length === 0) {
      log('🔌 Wallet disconnected');
      updateConnectionStatus(false, 'Disconnected');
    } else {
      log('🔄 Account changed, reconnecting...');
      init();
    }
  });

  window.ethereum.on('chainChanged', (chainId) => {
    log(`🌐 Network changed to chain ID: ${chainId}`);
    log('🔄 Reconnecting...');
    window.location.reload();
  });
}

// No additional CSS needed - using inline styles consistent with register.html

// Initialize when page loads
window.addEventListener('load', init);
</script>

</body>
</html>
