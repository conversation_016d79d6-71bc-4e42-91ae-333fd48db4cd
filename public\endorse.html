<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>✍️ Endorse Document - BDIO Registry</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<link rel="stylesheet" href="style.css">
</head>
<body>
<div class="container">
  <header class="header">
    <h1><i class="fas fa-signature"></i> Document Endorsement</h1>
    <p class="subtitle">Sign and endorse documents with blockchain verification</p>
  </header>


  <div class="main-content">
    <!-- Connection Status -->
    <div class="connection-status" id="connectionStatus">
      <span class="status-dot status-disconnected"></span>
      <span>Wallet Status: <span id="walletAddress">Not connected</span></span>
    </div>

    <!-- Form Section -->
    <div class="glass-card mb-4">
      <h3 class="section-title mb-3">
        <i class="fas fa-edit"></i> Document Endorsement
      </h3>

      <div class="form-group">
        <label class="form-label">
          <i class="fas fa-fingerprint"></i> BDIO Document ID *
        </label>
        <input
          type="text"
          id="bdioId"
          class="form-input"
          placeholder="Enter BDIO ID (e.g., abc123...)"
        />
      </div>

      <div class="form-group">
        <label class="form-label">
          <i class="fas fa-sticky-note"></i> Endorsement Note (Optional)
        </label>
        <textarea
          id="note"
          class="form-input form-textarea"
          rows="4"
          placeholder="Add your endorsement note or comments..."
        ></textarea>
        <small class="text-sm opacity-75">This note will be permanently stored on the blockchain</small>
      </div>

      <!-- Fee Information -->
      <div class="fee-info">
        <i class="fas fa-info-circle"></i>
        <span>Endorsement Fee: <span id="feeDisplay">Loading...</span></span>
      </div>

      <!-- Action Button -->
      <button onclick="endorse()" class="btn btn-success btn-large" id="signBtn" disabled>
        <i class="fas fa-signature"></i> Sign & Endorse Document
      </button>

      <!-- Utility Button -->
      <button onclick="loadEndorsements()" class="btn btn-outline mt-2">
        <i class="fas fa-list"></i> Load All Endorsements
      </button>
    </div>

    <!-- Document Info Section -->
    <div class="glass-card mb-4" id="documentInfo" style="display: none;">
      <h3 class="section-title mb-3">
        <i class="fas fa-file-alt"></i> Document Information
      </h3>
      <div id="docDetails"></div>
    </div>

    <!-- Status Section -->
    <div class="glass-card mb-4">
      <h3 class="section-title mb-3">
        <i class="fas fa-info-circle"></i> Activity Log
      </h3>
      <div id="status" class="log-container" style="height: 200px; background: #f8f9fa; color: #333;">
        Ready to endorse documents. Please connect your wallet to get started.
      </div>
    </div>

    <!-- Endorsements List -->
    <div class="glass-card" id="endorsementsSection" style="display: none;">
      <h3 class="section-title mb-3">
        <i class="fas fa-users"></i> Document Endorsements
      </h3>
      <div class="endorsements-list" id="endorsementList"></div>
    </div>
  </div>
</div>

<script>
// Application Configuration
const APP_CONFIG = {
  MIN_BDIO_ID_LENGTH: 10,
  MAX_BDIO_ID_LENGTH: 64,
  MAX_NOTE_LENGTH: 500,
  DEFAULT_ENDORSEMENT_FEE: '0.15'
};

// Contract addresses - loaded from server configuration
let endorsementAddress = null;
let accessControlAddress = null;
let bdioCoreAddress = null;

// Load contract configuration
async function loadConfig() {
  try {
    const response = await fetch('/api/config');
    if (response.ok) {
      const config = await response.json();
      endorsementAddress = config.endorsement;
      accessControlAddress = config.accessControl;
      bdioCoreAddress = config.address;
      console.log('✅ Contract configuration loaded');
      return true;
    }
  } catch (error) {
    console.warn('⚠️ Using fallback addresses:', error.message);
    // Fallback addresses
    endorsementAddress = '0xd87a5d71bb96E10f057B3E15464F1bEe5Bf71ECf';
    accessControlAddress = '0x1abfAE2B9b67B30EC092B0ca3D895495C6D32129';
    bdioCoreAddress = '0xC954fD7aC0cAeaD6BF54e5d9d8123b8D5E87FE6f';
    return false;
  }
}

let provider, signer, endorsementContract, accessControlContract, bdioContract;
let currentEndorsementFee = APP_CONFIG.DEFAULT_ENDORSEMENT_FEE;

// Complete ABI for the functions we need
const endorsementAbi = [
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "string", "name": "note", "type": "string"}],
    "name": "signWithNote",
    "outputs": [],
    "stateMutability": "payable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "getEndorsementsCount",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "uint256", "name": "index", "type": "uint256"}],
    "name": "getEndorsementByIndex",
    "outputs": [
      {"internalType": "address", "name": "signer", "type": "address"},
      {"internalType": "string", "name": "note", "type": "string"},
      {"internalType": "uint256", "name": "timestamp", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "endorsementFee",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  }
];

const accessControlAbi = [
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}],
    "name": "isSignerApproved",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "view",
    "type": "function"
  }
];

const bdioAbi = [
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "verifyDocument",
    "outputs": [
      {"internalType": "address", "name": "owner", "type": "address"},
      {"internalType": "bool", "name": "active", "type": "bool"},
      {"internalType": "bool", "name": "archived", "type": "bool"},
      {"internalType": "uint256", "name": "timestamp", "type": "uint256"},
      {"internalType": "uint256", "name": "versionCount", "type": "uint256"},
      {"internalType": "string", "name": "category", "type": "string"},
      {"internalType": "string", "name": "metadataUri", "type": "string"}
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

// Initialize the application
async function init() {
  log('🚀 Initializing Document Endorsement...');

  // Load contract configuration first
  await loadConfig();

  if (!endorsementAddress || !accessControlAddress || !bdioCoreAddress) {
    log('❌ Contract addresses not available. Please check server configuration.');
    return;
  }

  if (!window.ethereum) {
    log('❌ MetaMask not detected. Please install MetaMask extension.');
    updateConnectionStatus(false, 'MetaMask not found');
    return;
  }

  try {
    updateConnectionStatus('loading', 'Connecting...');
    log('🔗 Connecting to MetaMask...');

    provider = new ethers.BrowserProvider(window.ethereum);
    await provider.send("eth_requestAccounts", []);
    signer = await provider.getSigner();

    endorsementContract = new ethers.Contract(endorsementAddress, endorsementAbi, signer);
    accessControlContract = new ethers.Contract(accessControlAddress, accessControlAbi, signer);
    bdioContract = new ethers.Contract(bdioCoreAddress, bdioAbi, signer);

    const address = await signer.getAddress();
    const network = await provider.getNetwork();

    log(`✅ Connected successfully!`);
    log(`👤 Wallet: ${address}`);
    log(`🌐 Network: ${network.name} (Chain ID: ${network.chainId})`);
    log(`📋 Endorsement: ${endorsementAddress}`);
    log(`📋 AccessControl: ${accessControlAddress}`);
    log(`📋 BDIO Registry: ${bdioCoreAddress}`);

    updateConnectionStatus(true, address);

    // Load endorsement fee
    try {
      const fee = await endorsementContract.endorsementFee();
      currentEndorsementFee = ethers.formatEther(fee);
      document.getElementById('feeDisplay').textContent = `${currentEndorsementFee} POL`;
      log(`💰 Endorsement fee: ${currentEndorsementFee} POL`);
    } catch (e) {
      console.warn("Could not fetch endorsement fee from contract, using default");
      document.getElementById('feeDisplay').textContent = `${APP_CONFIG.DEFAULT_ENDORSEMENT_FEE} POL (fallback)`;
      log(`💰 Using fallback endorsement fee: ${APP_CONFIG.DEFAULT_ENDORSEMENT_FEE} POL`);
    }

    enableForm();

  } catch (error) {
    console.error('Connection error:', error);
    log(`❌ Connection failed: ${error.message}`);
    updateConnectionStatus(false, 'Connection failed');
  }
}

// Update connection status indicator
function updateConnectionStatus(connected, address = '') {
  const statusEl = document.querySelector('.status-dot');
  const addressEl = document.getElementById('walletAddress');

  if (connected === 'loading') {
    statusEl.className = 'status-dot status-loading';
    addressEl.textContent = address;
  } else if (connected) {
    statusEl.className = 'status-dot status-connected';
    addressEl.textContent = `${address.slice(0, 6)}...${address.slice(-4)}`;
  } else {
    statusEl.className = 'status-dot status-disconnected';
    addressEl.textContent = address || 'Not connected';
  }
}

// Enable form after successful connection
function enableForm() {
  document.getElementById('signBtn').disabled = false;
}

async function endorse() {
  try {
    const bdioId = getInput('bdioId');
    const note = document.getElementById('note').value.trim() || '';

    if (!endorsementContract) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    log('🔍 Verifying document and permissions...');

    // Verify document exists and get info
    const docInfo = await bdioContract.verifyDocument(bdioId);
    const owner = docInfo[0];
    const active = docInfo[1];
    const archived = docInfo[2];

    log(`📄 Document found - Owner: ${owner.slice(0, 6)}...${owner.slice(-4)}`);
    log(`📊 Status: ${active ? 'Active' : 'Inactive'}, ${archived ? 'Archived' : 'Not archived'}`);

    // Check if document is active
    if (!active) {
      log('❌ Cannot endorse: Document is inactive.');
      return;
    }

    if (archived) {
      log('❌ Cannot endorse: Document is archived.');
      return;
    }

    // Check if user is approved signer
    const myAddr = await signer.getAddress();
    log(`👤 Checking approval for: ${myAddr}`);

    const approved = await accessControlContract.isSignerApproved(bdioId, myAddr);
    if (!approved) {
      log('❌ Access denied: You are not an approved signer for this document.');
      log('💡 Tip: Ask the document owner to approve you as a signer first.');
      return;
    }

    log('✅ Approval verified. Preparing endorsement...');

    // Calculate fee
    const feeWei = ethers.parseEther(currentEndorsementFee);
    log(`💰 Endorsement fee: ${currentEndorsementFee} POL`);

    // Send endorsement transaction
    log('📡 Sending endorsement transaction...');

    const tx = await endorsementContract.signWithNote(bdioId, note, {
      value: feeWei
    });

    log(`⏳ Transaction sent: ${tx.hash}`);
    log('⏳ Waiting for confirmation...');

    const receipt = await tx.wait();

    log(`✅ Document endorsed successfully!`);
    log(`📦 Block number: ${receipt.blockNumber}`);
    log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);
    log(`🔗 Transaction: ${tx.hash}`);

    if (note) {
      log(`📝 Note: "${note}"`);
    }

    // Clear form
    document.getElementById('bdioId').value = '';
    document.getElementById('note').value = '';

    log('🎉 Ready for next endorsement!');

    // Auto-load endorsements
    await loadEndorsements();

  } catch (error) {
    console.error('Endorsement error:', error);

    let errorMessage = 'Unknown error occurred';
    if (error.reason) {
      errorMessage = error.reason;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.data?.message) {
      errorMessage = error.data.message;
    }

    log(`❌ Endorsement failed: ${errorMessage}`);

    // Handle specific errors
    if (errorMessage.includes('insufficient funds')) {
      log('💡 Tip: Make sure you have enough POL for the endorsement fee');
    } else if (errorMessage.includes('user rejected')) {
      log('💡 Transaction was cancelled by user');
    } else if (errorMessage.includes('Signer not approved')) {
      log('💡 You need to be approved as a signer for this document first');
    } else if (errorMessage.includes('Document not found')) {
      log('💡 Please check the BDIO ID and try again');
    }
  }
}

async function loadEndorsements() {
  try {
    const bdioId = getInput('bdioId');

    if (!endorsementContract) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    log('📡 Loading endorsements...');

    // Verify document exists first
    try {
      const docInfo = await bdioContract.verifyDocument(bdioId);
      const owner = docInfo[0];
      log(`📄 Document found - Owner: ${owner.slice(0, 6)}...${owner.slice(-4)}`);
    } catch (e) {
      log('❌ Document not found or invalid BDIO ID');
      return;
    }

    const count = await endorsementContract.getEndorsementsCount(bdioId);
    const endorsementsSection = document.getElementById('endorsementsSection');
    const list = document.getElementById('endorsementList');

    list.innerHTML = '';

    if (count == 0) {
      list.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-signature"></i>
          <h4>No Endorsements</h4>
          <p>This document has no endorsements yet.</p>
        </div>
      `;
    } else {
      for (let i = 0; i < count; i++) {
        const endorsement = await endorsementContract.getEndorsementByIndex(bdioId, i);
        const signerAddr = endorsement[0];
        const note = endorsement[1];
        const timestamp = endorsement[2];

        const date = new Date(Number(timestamp) * 1000);

        const endorsementCard = document.createElement('div');
        endorsementCard.className = 'glass-card mb-3 p-4';
        endorsementCard.innerHTML = `
          <div class="endorsement-header mb-2">
            <div class="flex justify-between items-start">
              <div class="endorsement-signer">
                <div class="flex items-center mb-1">
                  <i class="fas fa-user text-blue-500 mr-2"></i>
                  <span class="font-mono text-sm">${signerAddr}</span>
                </div>
                <div class="endorsement-meta">
                  <span class="status-indicator status-active">
                    <i class="fas fa-check"></i> Endorsed
                  </span>
                  <span class="text-sm opacity-75">${date.toLocaleString()}</span>
                </div>
              </div>
              <span class="text-sm opacity-75">#${i + 1}</span>
            </div>
          </div>
          ${note ? `
            <div class="endorsement-note">
              <div class="text-sm font-medium mb-1">
                <i class="fas fa-quote-left text-gray-500 mr-1"></i>
                Note:
              </div>
              <div class="bg-gray-100 p-3 rounded text-sm italic">
                "${note}"
              </div>
            </div>
          ` : ''}
        `;
        list.appendChild(endorsementCard);
      }
    }

    endorsementsSection.style.display = 'block';
    log(`✅ Found ${count} endorsement(s) for document ${bdioId}`);

  } catch (error) {
    console.error('Load endorsements error:', error);
    log(`❌ Error loading endorsements: ${error.message}`);
  }
}

function getInput(id) {
  const val = document.getElementById(id).value.trim();
  if (!val) {
    log(`❌ ${id} is required`);
    throw new Error(`${id} is required`);
  }

  // Validate BDIO ID length
  if (id === 'bdioId' && (val.length < APP_CONFIG.MIN_BDIO_ID_LENGTH || val.length > APP_CONFIG.MAX_BDIO_ID_LENGTH)) {
    log(`❌ BDIO ID must be between ${APP_CONFIG.MIN_BDIO_ID_LENGTH}-${APP_CONFIG.MAX_BDIO_ID_LENGTH} characters`);
    throw new Error('Invalid BDIO ID length');
  }

  // Validate note length
  if (id === 'note' && val.length > APP_CONFIG.MAX_NOTE_LENGTH) {
    log(`❌ Note must be less than ${APP_CONFIG.MAX_NOTE_LENGTH} characters`);
    throw new Error('Note too long');
  }

  return val;
}

function log(msg) {
  const el = document.getElementById('status');
  const timestamp = new Date().toLocaleTimeString();
  el.textContent += `[${timestamp}] ${msg}\n`;
  el.scrollTop = el.scrollHeight;
}

// Handle wallet account changes
if (window.ethereum) {
  window.ethereum.on('accountsChanged', (accounts) => {
    if (accounts.length === 0) {
      log('🔌 Wallet disconnected');
      updateConnectionStatus(false, 'Disconnected');
    } else {
      log('🔄 Account changed, reconnecting...');
      init();
    }
  });

  window.ethereum.on('chainChanged', (chainId) => {
    log(`🌐 Network changed to chain ID: ${chainId}`);
    log('🔄 Reconnecting...');
    window.location.reload();
  });
}

// Add some CSS for the endorsement cards
const style = document.createElement('style');
style.textContent = `
  .endorsement-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  }

  .endorsement-header {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 10px;
  }

  .endorsement-signer {
    flex: 1;
  }

  .endorsement-meta {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 5px;
  }

  .endorsement-note {
    margin-top: 10px;
  }

  .justify-between {
    justify-content: space-between;
  }

  .items-start {
    align-items: flex-start;
  }

  .items-center {
    align-items: center;
  }

  .text-blue-500 {
    color: #3b82f6;
  }

  .text-gray-500 {
    color: #6b7280;
  }

  .bg-gray-100 {
    background-color: #f3f4f6;
  }
`;
document.head.appendChild(style);

// Initialize when page loads
window.addEventListener('load', init);
</script>

</body>
</html>
