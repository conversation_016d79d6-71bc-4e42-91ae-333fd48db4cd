<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>📝 Endorse Document</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<style>
  body { font-family: 'Segoe UI', Tahoma, sans-serif; background: #f9fafb; color: #333;
    max-width: 500px; margin: 2em auto; padding: 2em; border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1); background-color: #fff; }
  h2 { text-align: center; color: #1e40af; margin-bottom: 1em; }
  label { margin-top: 1em; display: block; font-weight: 500; }
  input[type="text"], textarea { width: 100%; padding: 0.6em; margin-top: 0.3em;
    border: 1px solid #ccc; border-radius: 6px; font-size: 1em; }
  button { width: 100%; padding: 0.8em; margin-top: 1em; background: #1e40af; color: #fff;
    border: none; border-radius: 6px; font-weight: 600; cursor: pointer; transition: background 0.2s; font-size: 1em; }
  button:hover { background: #1a3691; }
  #status { margin-top: 1em; padding: 0.8em; background: #f1f5f9; border-left: 4px solid #1e40af;
    border-radius: 4px; font-size: 0.95em; min-height: 3em; white-space: pre-wrap; }
  ul { margin-top: 1em; padding-left: 1.2em; }
  .small { font-size: 0.9em; color: #666; }
</style>
</head>
<body>

<h2>📝 Endorse Document</h2>

<label>BDIO ID *</label>
<input type="text" id="bdioId" placeholder="e.g. abc123..." />

<label>Note (optional)</label>
<textarea id="note" rows="2" placeholder="Add a note about your endorsement"></textarea>

<button onclick="endorse()">✅ Sign & Endorse</button>
<button onclick="loadEndorsements()">📋 Load Endorsements</button>

<div id="status">Ready</div>
<ul id="endorsementList"></ul>
<div class="small">Make sure your wallet is connected and you are an approved signer.</div>

<script>
// Contract addresses - loaded from server configuration
let endorsementAddress = null;
let accessControlAddress = null;

// Load contract configuration
async function loadConfig() {
  try {
    const response = await fetch('/api/config');
    if (response.ok) {
      const config = await response.json();
      endorsementAddress = config.endorsement;
      accessControlAddress = config.accessControl;
      console.log('✅ Contract configuration loaded');
      return true;
    }
  } catch (error) {
    console.warn('⚠️ Using fallback addresses:', error.message);
    // Fallback addresses
    endorsementAddress = '******************************************';
    accessControlAddress = '******************************************';
    return false;
  }
}

let provider, signer, endorsementContract, accessControlContract;
let endorsementAbi, accessAbi;

async function loadABIs() {
  try {
    endorsementAbi = await (await fetch('abis/EndorsementManager.json')).json();
    accessAbi = await (await fetch('abis/AccessControlManager.json')).json();
  } catch (e) { log('❌ Failed to load ABIs: ' + e); }
}

async function init() {
  // Load contract configuration first
  await loadConfig();
  await loadABIs();
  if (!window.ethereum) return log('❌ MetaMask not found.');
  provider = new ethers.BrowserProvider(window.ethereum);
  await provider.send("eth_requestAccounts", []);
  signer = await provider.getSigner();
  endorsementContract = new ethers.Contract(endorsementAddress, endorsementAbi.abi, signer);
  accessControlContract = new ethers.Contract(accessControlAddress, accessAbi.abi, signer);
  log('✅ Wallet connected: ' + await signer.getAddress());
}

async function endorse() {
  try {
    const bdioId = getInput('bdioId');
    const note = document.getElementById('note').value.trim();
    const signerAddr = await signer.getAddress();

    log('🔍 Checking if you are approved signer...');
    const approved = await accessControlContract.isSignerApproved(bdioId, signerAddr);
    if (!approved) return log('❌ You are not approved to endorse.');

    // compute keccak256(abi.encodePacked(bdioId))
    const hash = ethers.keccak256(ethers.toUtf8Bytes(bdioId));
    const signature = await signer.signMessage(ethers.getBytes(hash));

    log('📡 Sending signWithNote...');
    const tx = await endorsementContract.signWithNote(bdioId, signature, note);
    await tx.wait();
    log('✅ Endorsed! TxHash: ' + tx.hash);

    document.getElementById('note').value = '';
    await loadEndorsements();
  } catch (e) { log('❌ ' + (e?.message || e)); console.error(e); }
}

async function loadEndorsements() {
  try {
    const bdioId = getInput('bdioId');
    log('📡 Loading endorsements...');
    const count = await endorsementContract.getEndorsementsCount(bdioId);
    const list = document.getElementById('endorsementList'); list.innerHTML = '';
    for (let i=0; i<count; i++) {
      const e = await endorsementContract.getEndorsementByIndex(bdioId, i);
      const li = document.createElement('li');
      li.textContent = `Signer: ${e[0]}, Note: "${e[3]}", At: ${new Date(Number(e[2])*1000).toLocaleString()}`;
      list.appendChild(li);
    }
    log(`✅ Loaded ${count} endorsement(s).`);
  } catch (e) { log('❌ ' + (e?.message || e)); console.error(e); }
}

function getInput(id) {
  const val = document.getElementById(id).value.trim();
  if (!val) throw '⚠️ Please enter BDIO ID.';
  return val;
}

function log(msg) {
  document.getElementById('status').innerText = msg;
}

window.onload = init;
</script>

</body>
</html>
