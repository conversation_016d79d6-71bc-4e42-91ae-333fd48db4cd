<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>📅 Document Expiry Manager - BDIO Registry</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<link rel="stylesheet" href="style.css">
<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
  }

  .container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
  }

  .header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 300;
  }

  .header p {
    opacity: 0.9;
    font-size: 1.1rem;
  }

  .form-container {
    padding: 40px;
  }

  .form-group {
    margin-bottom: 25px;
  }

  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
  }

  .required {
    color: #e74c3c;
  }

  .form-control {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
  }

  .form-control:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  textarea.form-control {
    min-height: 100px;
    resize: vertical;
  }

  .btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 18px 30px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
  }

  .btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }

  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .btn-success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  }

  .btn-success:hover:not(:disabled) {
    box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
  }

  .btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
  }

  .btn-outline:hover:not(:disabled) {
    background: #667eea;
    color: white;
  }

  .wallet-info {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
    border: 1px solid #4caf50;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .wallet-info i {
    color: #4caf50;
  }

  .mode-selector {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 25px;
  }

  .mode-option {
    padding: 20px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
  }

  .mode-option.active {
    border-color: #667eea;
    background: #f0f4ff;
    color: #667eea;
  }

  .mode-option:hover {
    border-color: #667eea;
  }

  .status-container {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
  }

  .status-title {
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
  }

  .status-content {
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #666;
    line-height: 1.5;
    max-height: 200px;
    overflow-y: auto;
  }

  .expiry-info {
    background: #fff3cd;
    border: 1px solid #ffc107;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .expiry-info i {
    color: #856404;
  }

  @media (max-width: 768px) {
    .container {
      margin: 10px;
      border-radius: 15px;
    }

    .form-container {
      padding: 20px;
    }

    .header {
      padding: 20px;
    }

    .header h1 {
      font-size: 2rem;
    }

    .mode-selector {
      grid-template-columns: 1fr;
    }
  }
</style>
</head>
<body>
<div class="container">
  <div class="header">
    <h1><i class="fas fa-calendar-alt"></i> Document Expiry Manager</h1>
    <p>Set and manage document expiration dates</p>
  </div>
  <div class="form-container">
    <!-- Wallet Connection Status -->
    <div id="walletInfo" class="wallet-info">
      <i class="fas fa-wallet"></i> <strong>Wallet Connected:</strong> <span id="walletAddress">Not connected</span>
    </div>

    <!-- Mode Selection -->
    <div class="form-group">
      <label>Operation Mode <span class="required">*</span></label>
      <div class="mode-selector">
        <div class="mode-option active" data-mode="single">
          <i class="fas fa-file"></i>
          <div><strong>Single Document</strong></div>
          <small>Set expiry for one document</small>
        </div>
        <div class="mode-option" data-mode="multiple">
          <i class="fas fa-files"></i>
          <div><strong>Multiple Documents</strong></div>
          <small>Batch set expiry for documents</small>
        </div>
      </div>
    </div>

    <!-- Single Document Fields -->
    <div id="singleFields">
      <div class="form-group">
        <label for="bdioIdInput">BDIO Document ID <span class="required">*</span></label>
        <input
          type="text"
          id="bdioIdInput"
          class="form-control"
          placeholder="Enter BDIO ID (e.g., abc123...)"
        />
      </div>

      <div class="form-group">
        <label for="expiryInput">Expiry Date and Time (Optional)</label>
        <input
          type="datetime-local"
          id="expiryInput"
          class="form-control"
        />
        <small style="color: #666; margin-top: 5px; display: block;">
          Leave blank for no expiry. Must be a future date.
        </small>
      </div>
    </div>

    <!-- Multiple Documents Fields -->
    <div id="multiFields" style="display:none;">
      <div class="form-group">
        <label for="bdioIdsInput">BDIO Document IDs <span class="required">*</span></label>
        <textarea
          id="bdioIdsInput"
          class="form-control"
          rows="6"
          placeholder="Enter one BDIO ID per line:&#10;abc123...&#10;def456...&#10;ghi789..."
        ></textarea>
        <small style="color: #666; margin-top: 5px; display: block;">
          Enter one BDIO ID per line (maximum 50 documents)
        </small>
      </div>

      <div class="form-group">
        <label for="batchExpiryInput">Expiry Date and Time (Optional)</label>
        <input
          type="datetime-local"
          id="batchExpiryInput"
          class="form-control"
        />
        <small style="color: #666; margin-top: 5px; display: block;">
          This expiry will be applied to all documents. Leave blank for no expiry.
        </small>
      </div>
    </div>

    <!-- Expiry Information -->
    <div class="expiry-info">
      <i class="fas fa-info-circle"></i>
      <div>
        <strong>Note:</strong> Only document owners can set expiry dates.
        Setting expiry to blank will remove any existing expiry.
      </div>
    </div>

    <!-- Action Buttons -->
    <button onclick="setExpiry()" class="btn btn-success" id="setExpiryBtn">
      <i class="fas fa-calendar-check"></i> Set Document Expiry
    </button>

    <button onclick="checkExpiry()" class="btn btn-outline">
      <i class="fas fa-search"></i> Check Current Expiry
    </button>

    <button onclick="clearExpiry()" class="btn btn-outline">
      <i class="fas fa-calendar-times"></i> Clear Expiry
    </button>

    <!-- Status Section -->
    <div class="status-container">
      <div class="status-title">
        <i class="fas fa-info-circle"></i> Activity Log
      </div>
      <div id="status" class="status-content">
        Ready to manage document expiry. Please connect your wallet to get started.
      </div>
    </div>
  </div>
</div>

<script>
// Application Configuration
const APP_CONFIG = {
  MIN_BDIO_ID_LENGTH: 10,
  MAX_BDIO_ID_LENGTH: 64,
  MAX_BATCH_SIZE: 50
};

// Contract addresses - will be loaded from server
let expiryManagerAddress = null;
let bdioCoreAddress = null;

// Load contract configuration
async function loadContractConfig() {
  try {
    const response = await fetch('/api/config');
    if (response.ok) {
      const config = await response.json();
      expiryManagerAddress = config.expiry;
      bdioCoreAddress = config.address;
      console.log('✅ Contract configuration loaded');
      return true;
    } else {
      throw new Error('Server config not available');
    }
  } catch (error) {
    console.warn('⚠️ Using fallback contract addresses');
    expiryManagerAddress = '0xYourExpiryManagerAddress';
    bdioCoreAddress = '0xYourBDIOCoreAddress';
    return false;
  }
}

// Complete ABI for the functions we need
const expiryAbi = [
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "uint256", "name": "expiryTimestamp", "type": "uint256"}],
    "name": "setExpiry",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string[]", "name": "bdioIds", "type": "string[]"}, {"internalType": "uint256[]", "name": "timestamps", "type": "uint256[]"}],
    "name": "batchSetExpiry",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "clearExpiry",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "getExpiry",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "isExpired",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "view",
    "type": "function"
  }
];

const bdioAbi = [
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "verifyDocument",
    "outputs": [
      {"internalType": "address", "name": "owner", "type": "address"},
      {"internalType": "bool", "name": "active", "type": "bool"},
      {"internalType": "bool", "name": "archived", "type": "bool"},
      {"internalType": "uint256", "name": "timestamp", "type": "uint256"},
      {"internalType": "uint256", "name": "versionCount", "type": "uint256"},
      {"internalType": "string", "name": "category", "type": "string"},
      {"internalType": "string", "name": "metadataUri", "type": "string"}
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

let provider, signer, expiryManager, bdioContract;

// Initialize the application
async function init() {
  log('🚀 Initializing Document Expiry Manager...');

  // Load contract configuration first
  await loadContractConfig();

  if (!expiryManagerAddress || !bdioCoreAddress) {
    log('❌ Contract addresses not available. Please check server configuration.');
    return;
  }

  if (!window.ethereum) {
    log('❌ MetaMask not detected. Please install MetaMask extension.');
    updateConnectionStatus(false, 'MetaMask not found');
    return;
  }

  try {
    updateConnectionStatus('loading', 'Connecting...');
    log('🔗 Connecting to MetaMask...');

    provider = new ethers.BrowserProvider(window.ethereum);
    await provider.send("eth_requestAccounts", []);
    signer = await provider.getSigner();

    expiryManager = new ethers.Contract(expiryManagerAddress, expiryAbi, signer);
    bdioContract = new ethers.Contract(bdioCoreAddress, bdioAbi, signer);

    const address = await signer.getAddress();
    const network = await provider.getNetwork();

    log(`✅ Connected successfully!`);
    log(`👤 Wallet: ${address}`);
    log(`🌐 Network: ${network.name} (Chain ID: ${network.chainId})`);
    log(`📋 Expiry Manager: ${expiryManagerAddress}`);
    log(`📋 BDIO Registry: ${bdioCoreAddress}`);

    updateConnectionStatus(true, address);

    // Setup mode selector
    setupModeSelector();

  } catch (error) {
    console.error('Connection error:', error);
    log(`❌ Connection failed: ${error.message}`);
    updateConnectionStatus(false, 'Connection failed');
  }
}

// Update connection status indicator
function updateConnectionStatus(connected, address = '') {
  const walletInfo = document.getElementById('walletInfo');
  const addressEl = document.getElementById('walletAddress');

  if (connected === 'loading') {
    addressEl.textContent = 'Connecting...';
    walletInfo.style.background = 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)';
    walletInfo.style.borderColor = '#ffc107';
  } else if (connected) {
    addressEl.textContent = `${address.slice(0, 6)}...${address.slice(-4)}`;
    walletInfo.style.background = 'linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%)';
    walletInfo.style.borderColor = '#4caf50';
  } else {
    addressEl.textContent = address || 'Not connected';
    walletInfo.style.background = 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)';
    walletInfo.style.borderColor = '#dc3545';
  }
}

// Setup mode selector
function setupModeSelector() {
  const modeOptions = document.querySelectorAll('.mode-option');
  const singleFields = document.getElementById('singleFields');
  const multiFields = document.getElementById('multiFields');

  modeOptions.forEach(option => {
    option.addEventListener('click', () => {
      // Remove active class from all options
      modeOptions.forEach(opt => opt.classList.remove('active'));
      // Add active class to clicked option
      option.classList.add('active');

      const mode = option.dataset.mode;
      if (mode === 'single') {
        singleFields.style.display = 'block';
        multiFields.style.display = 'none';
      } else {
        singleFields.style.display = 'none';
        multiFields.style.display = 'block';
      }
    });
  });
}

async function setExpiry() {
  try {
    if (!expiryManager) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    const activeMode = document.querySelector('.mode-option.active').dataset.mode;

    if (activeMode === 'single') {
      await setSingleExpiry();
    } else {
      await setBatchExpiry();
    }

  } catch (error) {
    console.error('Set expiry error:', error);
    log(`❌ Error setting expiry: ${error.message}`);
  }
}

async function setSingleExpiry() {
  const bdioId = getInput('bdioIdInput');
  const expiryStr = document.getElementById('expiryInput').value;

  log('🔍 Verifying document ownership...');

  // Verify document exists and get owner
  const docInfo = await bdioContract.verifyDocument(bdioId);
  const owner = docInfo[0];
  const active = docInfo[1];

  const myAddr = await signer.getAddress();

  log(`📄 Document found - Owner: ${owner.slice(0, 6)}...${owner.slice(-4)}`);
  log(`📊 Status: ${active ? 'Active' : 'Inactive'}`);

  // Check ownership
  if (myAddr.toLowerCase() !== owner.toLowerCase()) {
    log('❌ Access denied: You are not the owner of this document.');
    return;
  }

  // Validate expiry date
  let expiryTimestamp = 0;
  if (expiryStr) {
    const expiryDate = new Date(expiryStr);
    const now = new Date();

    if (expiryDate <= now) {
      log('❌ Expiry date must be in the future.');
      return;
    }

    expiryTimestamp = Math.floor(expiryDate.getTime() / 1000);
    log(`📅 Setting expiry to: ${expiryDate.toLocaleString()}`);
  } else {
    log('📅 Removing expiry (no expiration)');
  }

  log('📡 Sending setExpiry transaction...');
  const tx = await expiryManager.setExpiry(bdioId, expiryTimestamp);
  log(`⏳ Transaction sent: ${tx.hash}`);
  log('⏳ Waiting for confirmation...');

  const receipt = await tx.wait();

  log(`✅ Expiry set successfully!`);
  log(`📦 Block number: ${receipt.blockNumber}`);
  log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);
  log(`🔗 Transaction: ${tx.hash}`);
}

async function setBatchExpiry() {
  const bdioIdsText = getInput('bdioIdsInput');
  const expiryStr = document.getElementById('batchExpiryInput').value;

  const bdioIds = bdioIdsText.split('\n')
    .map(id => id.trim())
    .filter(id => id.length > 0);

  if (bdioIds.length === 0) {
    log('❌ Please enter at least one BDIO ID.');
    return;
  }

  if (bdioIds.length > APP_CONFIG.MAX_BATCH_SIZE) {
    log(`❌ Maximum ${APP_CONFIG.MAX_BATCH_SIZE} documents allowed per batch.`);
    return;
  }

  // Validate expiry date
  let expiryTimestamp = 0;
  if (expiryStr) {
    const expiryDate = new Date(expiryStr);
    const now = new Date();

    if (expiryDate <= now) {
      log('❌ Expiry date must be in the future.');
      return;
    }

    expiryTimestamp = Math.floor(expiryDate.getTime() / 1000);
    log(`📅 Setting expiry to: ${expiryDate.toLocaleString()}`);
  } else {
    log('📅 Removing expiry (no expiration)');
  }

  log(`🔍 Verifying ownership for ${bdioIds.length} documents...`);

  const myAddr = await signer.getAddress();
  const validBdioIds = [];

  // Verify ownership for each document
  for (let i = 0; i < bdioIds.length; i++) {
    try {
      const docInfo = await bdioContract.verifyDocument(bdioIds[i]);
      const owner = docInfo[0];

      if (myAddr.toLowerCase() === owner.toLowerCase()) {
        validBdioIds.push(bdioIds[i]);
        log(`✅ ${bdioIds[i]} - Ownership verified`);
      } else {
        log(`❌ ${bdioIds[i]} - Not owned by you`);
      }
    } catch (e) {
      log(`❌ ${bdioIds[i]} - Document not found`);
    }
  }

  if (validBdioIds.length === 0) {
    log('❌ No valid documents found that you own.');
    return;
  }

  log(`📡 Setting expiry for ${validBdioIds.length} documents...`);

  const timestamps = new Array(validBdioIds.length).fill(expiryTimestamp);

  const tx = await expiryManager.batchSetExpiry(validBdioIds, timestamps);
  log(`⏳ Transaction sent: ${tx.hash}`);
  log('⏳ Waiting for confirmation...');

  const receipt = await tx.wait();

  log(`✅ Batch expiry set successfully!`);
  log(`📦 Block number: ${receipt.blockNumber}`);
  log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);
  log(`🔗 Transaction: ${tx.hash}`);
  log(`📊 Updated ${validBdioIds.length} documents`);
}

async function checkExpiry() {
  try {
    if (!expiryManager) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    const activeMode = document.querySelector('.mode-option.active').dataset.mode;

    if (activeMode === 'single') {
      const bdioId = getInput('bdioIdInput');

      log('🔍 Checking expiry for document...');

      // Verify document exists
      try {
        await bdioContract.verifyDocument(bdioId);
      } catch (e) {
        log('❌ Document not found or invalid BDIO ID');
        return;
      }

      const expiryTimestamp = await expiryManager.getExpiry(bdioId);
      const isExpired = await expiryManager.isExpired(bdioId);

      if (expiryTimestamp == 0) {
        log(`📅 Document ${bdioId} has no expiry date (never expires)`);
      } else {
        const expiryDate = new Date(Number(expiryTimestamp) * 1000);
        log(`📅 Document ${bdioId} expires on: ${expiryDate.toLocaleString()}`);
        log(`📊 Status: ${isExpired ? '❌ EXPIRED' : '✅ Valid'}`);
      }
    } else {
      log('❌ Batch check not implemented. Please use single mode.');
    }

  } catch (error) {
    console.error('Check expiry error:', error);
    log(`❌ Error checking expiry: ${error.message}`);
  }
}

async function clearExpiry() {
  try {
    if (!expiryManager) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    const activeMode = document.querySelector('.mode-option.active').dataset.mode;

    if (activeMode !== 'single') {
      log('❌ Clear expiry only available in single mode.');
      return;
    }

    const bdioId = getInput('bdioIdInput');

    log('🔍 Verifying document ownership...');

    // Verify document exists and get owner
    const docInfo = await bdioContract.verifyDocument(bdioId);
    const owner = docInfo[0];

    const myAddr = await signer.getAddress();

    // Check ownership
    if (myAddr.toLowerCase() !== owner.toLowerCase()) {
      log('❌ Access denied: You are not the owner of this document.');
      return;
    }

    log('📡 Clearing expiry...');
    const tx = await expiryManager.clearExpiry(bdioId);
    log(`⏳ Transaction sent: ${tx.hash}`);
    log('⏳ Waiting for confirmation...');

    const receipt = await tx.wait();

    log(`✅ Expiry cleared successfully!`);
    log(`📦 Block number: ${receipt.blockNumber}`);
    log(`🔗 Transaction: ${tx.hash}`);

  } catch (error) {
    console.error('Clear expiry error:', error);
    log(`❌ Error clearing expiry: ${error.message}`);
  }
}

function getInput(id) {
  const val = document.getElementById(id).value.trim();
  if (!val) {
    log(`❌ ${id} is required`);
    throw new Error(`${id} is required`);
  }

  // Validate BDIO ID length
  if (id.includes('bdio') && (val.length < APP_CONFIG.MIN_BDIO_ID_LENGTH || val.length > APP_CONFIG.MAX_BDIO_ID_LENGTH)) {
    log(`❌ BDIO ID must be between ${APP_CONFIG.MIN_BDIO_ID_LENGTH}-${APP_CONFIG.MAX_BDIO_ID_LENGTH} characters`);
    throw new Error('Invalid BDIO ID length');
  }

  return val;
}

function log(msg) {
  const el = document.getElementById('status');
  const timestamp = new Date().toLocaleTimeString();
  el.textContent += `[${timestamp}] ${msg}\n`;
  el.scrollTop = el.scrollHeight;
}

// Handle wallet account changes
if (window.ethereum) {
  window.ethereum.on('accountsChanged', (accounts) => {
    if (accounts.length === 0) {
      log('🔌 Wallet disconnected');
      updateConnectionStatus(false, 'Disconnected');
    } else {
      log('🔄 Account changed, reconnecting...');
      init();
    }
  });

  window.ethereum.on('chainChanged', (chainId) => {
    log(`🌐 Network changed to chain ID: ${chainId}`);
    log('🔄 Reconnecting...');
    window.location.reload();
  });
}

// Initialize when page loads
window.addEventListener('load', init);
</script>
</body>
</html>
