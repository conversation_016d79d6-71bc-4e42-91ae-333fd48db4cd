<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>BDIO Document Detail</title>
<link rel="stylesheet" href="style.css">
</head>
<body>
<h1>BDIO Document Detail</h1>
<input type="text" id="bdioIdInput" placeholder="Enter BDIO ID">
<button id="loadButton" onclick="loadDocument()">Load Document</button>
<span id="loadingSpinner" style="display:none;" aria-label="Loading">Loading...</span>

<div id="documentInfo" style="display:none;">
  <h2>Document Info</h2>
  <button id="refreshButton" onclick="loadDocument()">Refresh Document Info</button>
  <p><strong>Title:</strong> <span id="title"></span></p>
  <p><strong>Category:</strong> <span id="category"></span></p>
  <p hidden><strong>Owner:</strong> <span id="owner"></span></p>
  <p><strong>Active:</strong> <span id="active"></span></p>
  <p hidden><strong>Archived:</strong> <span id="archived"></span></p>
  <p><strong>Metadata URI:</strong> <span id="metadataUri"></span></p>
  <p><strong>Blockchain:</strong> <span id="blockchainTxHash"></span></p>
  <p><strong>Initial Hash:</strong> <span id="initialHash"></span></p>
  <p><strong>Created At:</strong> <span id="createdAt"></span></p>
  <p><strong>VC Hash:</strong> <span id="vcHash"></span></p>
  <h3>NFT Info</h3>
  <p><strong>NFT Token ID:</strong> <span id="nftTokenId">Loading...</span></p>
  <p><strong>NFT Token URI:</strong> <span id="nftTokenURI">Loading...</span></p>

  <h3>Versions</h3>
  <ul id="versions"></ul>

  <h3>Ownership History</h3>
  <ul id="ownershipHistory"></ul>

  <h3>Signers</h3>
  <ul id="signersStatusList">Loading signers...</ul>

  <h3>Expiry Date</h3>
  <p id="expiryDate">Loading...</p>
  <div id="expiryUpdateDiv" style="display:none;">
    <input type="datetime-local" id="newExpiryInput" />
    <button id="setExpiryButton">Update Expiry Date</button>
  </div>

  <h3>Endorsements</h3>
  <ul id="endorsements"></ul>
</div>

<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<script>
// Contract addresses - loaded from server configuration
let bdioCoreAddress = null;
let accessControlAddress = null;
let endorsementAddress = null;
let vcManagerAddress = null;
let expiryManagerAddress = null;

// Load contract configuration
async function loadConfig() {
  try {
    const response = await fetch('/api/config');
    if (response.ok) {
      const config = await response.json();
      bdioCoreAddress = config.address;
      accessControlAddress = config.accessControl;
      endorsementAddress = config.endorsement;
      vcManagerAddress = config.vcManager;
      expiryManagerAddress = config.expiry;
      console.log('✅ Contract configuration loaded');
      return true;
    }
  } catch (error) {
    console.warn('⚠️ Using fallback addresses:', error.message);
    // Fallback addresses
    bdioCoreAddress = '******************************************';
    accessControlAddress = '******************************************';
    endorsementAddress = '******************************************';
    vcManagerAddress = '******************************************';
    expiryManagerAddress = '******************************************';
    return false;
  }
}

let provider, signer;
let bdioCore, accessControl, endorsementManager, vcManager, expiryManager;
let ownershipListenerAdded = false;

async function init() {
  // Load contract configuration first
  await loadConfig();

  provider = new ethers.BrowserProvider(window.ethereum);
  signer = await provider.getSigner();

  const [bdioAbi, accessAbi, endorseAbi, vcAbi, expiryAbi] = await Promise.all([
    fetch('/abis/BDIOCoreRegistry.json').then(res => res.json()),
    fetch('/abis/AccessControlManager.json').then(res => res.json()),
    fetch('/abis/EndorsementManager.json').then(res => res.json()),
    fetch('/abis/VCManager.json').then(res => res.json()),
    fetch('/abis/BDIOExpiryManager.json').then(res => res.json()),
  ]);

  bdioCore = new ethers.Contract(bdioCoreAddress, bdioAbi.abi, provider);
  accessControl = new ethers.Contract(accessControlAddress, accessAbi.abi, provider);
  endorsementManager = new ethers.Contract(endorsementAddress, endorseAbi.abi, provider);
  vcManager = new ethers.Contract(vcManagerAddress, vcAbi.abi, provider);
  expiryManager = new ethers.Contract(expiryManagerAddress, expiryAbi.abi, provider);

  // listen OwnershipTransferred once
  if (!ownershipListenerAdded) {
    bdioCore.on('OwnershipTransferred', async (bdioId, oldOwner, newOwner) => {
      console.log('OwnershipTransferred event:', bdioId);
      await fetch('/api/updateMetadata', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({ bdioId })
      });
      
    const currentBdioId = document.getElementById('bdioIdInput').value.trim();
      if (currentBdioId === bdioId) {
        loadDocument();
        }
      });
    ownershipListenerAdded = true;
  }

  const bdioIdParam = new URLSearchParams(window.location.search).get('bdioId');
  if (bdioIdParam) {
    document.getElementById('bdioIdInput').value = bdioIdParam;
    loadDocument();
  }
}

async function loadDocument() {
  const bdioId = document.getElementById('bdioIdInput').value.trim();
  if (!bdioId) return alert('Please enter BDIO ID');

  document.getElementById('loadingSpinner').style.display = 'inline-block';
  document.getElementById('loadButton').disabled = true;

  try {
    const [owner, active, archived, timestamp, , category] = await bdioCore.verifyDocument(bdioId);
    document.getElementById('owner').textContent = owner;
    document.getElementById('archived').textContent = archived;
    document.getElementById('createdAt').textContent = new Date(Number(timestamp)*1000).toLocaleString();
    document.getElementById('category').textContent = category;

    const metadataUrl = `/metadata/${bdioId}.meta.json`;
    document.getElementById('metadataUri').innerHTML = `<a href="${metadataUrl}" target="_blank">${metadataUrl}</a>`;

    // Metadata for tx hash
    try {
      const meta = await (await fetch(metadataUrl)).json();
      if (meta.blockchaintxHash)
        document.getElementById('blockchainTxHash').innerHTML = `<a href="https://polygonscan.com/tx/${meta.blockchaintxHash}" target="_blank">${meta.blockchaintxHash}</a>`;
    } catch { document.getElementById('blockchainTxHash').textContent = 'N/A'; }

    const { title, hashHex } = await bdioCore.registry(bdioId);
    document.getElementById('title').textContent = title;
    document.getElementById('initialHash').textContent = hashHex;

    const vcHash = await vcManager.vcHash(bdioId);
    document.getElementById('vcHash').textContent = vcHash || 'N/A';

    // Expiry
    const expiry = await expiryManager.getExpiry(bdioId);
    const expiryElem = document.getElementById('expiryDate');
    expiryElem.textContent = expiry>0 ? new Date(Number(expiry)*1000).toLocaleString() : 'No expiry set';
    document.getElementById('active').textContent = (expiry && Number(expiry)*1000 < Date.now()) ? 'Expired' : 'Active';

    // Versions
    const versions = await bdioCore.getDocumentVersions(bdioId);
    document.getElementById('versions').innerHTML = versions.map(v =>
      `<li>${v.hashHex} (by ${v.editor} at ${new Date(Number(v.timestamp)*1000).toLocaleString()})</li>`
    ).join('');

    // Ownership
    const history = await bdioCore.getOwnershipHistory(bdioId);
    document.getElementById('ownershipHistory').innerHTML = history.map(h =>
      `<li>${h.owner} - ${h.note} at ${new Date(Number(h.timestamp)*1000).toLocaleString()}</li>`
    ).join('');

    // Endorsements
    const count = await endorsementManager.getEndorsementsCount(bdioId);
    const items = [];
    for (let i=0;i<count;i++) {
      const e = await endorsementManager.getEndorsementByIndex(bdioId,i);
      items.push(`<li>${e[0]} - ${e[3]} at ${new Date(Number(e[2])*1000).toLocaleString()}</li>`);
    }
    document.getElementById('endorsements').innerHTML = items.join('');

    // Signers and Status
    const signersStatusList = document.getElementById('signersStatusList');
    signersStatusList.innerHTML = 'Loading signers...';
    try {
      const approvedSigners = await accessControl.getApprovedSigners(bdioId);
      // Build a set of signed addresses from endorsements
      const signedAddresses = new Set();
      for (let i=0; i<count; i++) {
        const e = await endorsementManager.getEndorsementByIndex(bdioId, i);
        signedAddresses.add(e[0].toLowerCase());
      }
      const signerItems = [];
      for (const signerAddr of approvedSigners) {
        const isApproved = await accessControl.isSignerApproved(bdioId, signerAddr);
        const lowerAddr = signerAddr.toLowerCase();
        let status = 'Revoked';
        if (isApproved) {
          status = signedAddresses.has(lowerAddr) ? 'Signed' : 'Awaiting Signature';
        }
        signerItems.push(`<li>${signerAddr} - ${status}</li>`);
      }
      signersStatusList.innerHTML = signerItems.join('');
    } catch (e) {
      signersStatusList.innerHTML = 'Failed to load signers: ' + (e.message || e);
    }

    // NFT
    await loadNFTInfo(bdioId);

    // Admin check for expiry update
    try {
      const admin = await accessControl.isAdmin(bdioId, await signer.getAddress());
      document.getElementById('expiryUpdateDiv').style.display = admin ? 'block' : 'none';
      if(admin){
        document.getElementById('setExpiryButton').onclick=async()=>{
          const newExpiry=Math.floor(new Date(document.getElementById('newExpiryInput').value).getTime()/1000);
          const tx=await expiryManager.connect(signer).setExpiry(bdioId,newExpiry);
          await tx.wait();
          // Call API to update metadata JSON file with new expiry
          await fetch('/api/updateMetadata', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({ bdioId })
          });
          alert('Expiry updated');
          loadDocument();
        }
      }
    }catch(e){console.warn('isAdmin check failed:',e)}

    document.getElementById('documentInfo').style.display='block';
  } catch (e) {
    console.error(e);
    alert('Failed to load: '+(e.message||e));
  } finally {
    document.getElementById('loadingSpinner').style.display='none';
    document.getElementById('loadButton').disabled=false;
  }
}

async function loadNFTInfo(bdioId) {
  try {
    const tokenId=await bdioCore.bdioToTokenId(bdioId);
    if(tokenId && tokenId!=0){
      document.getElementById('nftTokenId').textContent=tokenId;
      const uri=await bdioCore.tokenURI(tokenId);
      document.getElementById('nftTokenURI').textContent=uri;
    }else{
      document.getElementById('nftTokenId').textContent='No NFT minted';
      document.getElementById('nftTokenURI').textContent='N/A';
    }
  }catch(e){
    console.error('NFT info:',e);
    document.getElementById('nftTokenId').textContent='Error';
    document.getElementById('nftTokenURI').textContent='Error';
  }
}

window.onload=init;
</script>
</body>
</html>
