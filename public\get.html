<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>BDIO Document Detail</title>
<link rel="stylesheet" href="style.css">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
<div class="container">
  <header class="header">
    <h1><i class="fas fa-file-alt"></i> BDIO Document Detail</h1>
    <p class="subtitle">Blockchain Document Identity & Ownership Registry</p>
  </header>

  <div class="search-section">
    <div class="search-container">
      <input type="text" id="bdioIdInput" placeholder="Enter BDIO ID (e.g., 3a83259ef9)" class="search-input">
      <button id="loadButton" onclick="loadDocument()" class="search-btn">
        <i class="fas fa-search"></i> Load Document
      </button>
    </div>
    <div id="loadingSpinner" class="loading-spinner" style="display:none;">
      <i class="fas fa-spinner fa-spin"></i> Loading document details...
    </div>
  </div>

  <div id="documentInfo" class="document-info" style="display:none;">
    <!-- Document Status Banner -->
    <div id="statusBanner" class="status-banner">
      <div class="status-item">
        <i class="fas fa-circle" id="statusIcon"></i>
        <span id="statusText">Active</span>
      </div>
      <div class="status-item">
        <i class="fas fa-clock"></i>
        <span id="expiryStatus">No Expiry</span>
      </div>
    </div>

    <!-- Main Document Information -->
    <div class="info-grid">
      <div class="info-card">
        <h3><i class="fas fa-info-circle"></i> Document Information</h3>
        <div class="info-row">
          <span class="label">Title:</span>
          <span id="title" class="value">-</span>
        </div>
        <div class="info-row">
          <span class="label">Category:</span>
          <span id="category" class="value">-</span>
        </div>
        <div class="info-row">
          <span class="label">Owner:</span>
          <span id="owner" class="value address">-</span>
        </div>
        <div class="info-row">
          <span class="label">Created:</span>
          <span id="createdAt" class="value">-</span>
        </div>
        <div class="info-row">
          <span class="label">Initial Hash:</span>
          <span id="initialHash" class="value hash">-</span>
        </div>
      </div>

      <div class="info-card">
        <h3><i class="fas fa-link"></i> Blockchain & Metadata</h3>
        <div class="info-row">
          <span class="label">Metadata URI:</span>
          <span id="metadataUri" class="value link">-</span>
        </div>
        <div class="info-row">
          <span class="label">Blockchain Tx:</span>
          <span id="blockchainTxHash" class="value link">-</span>
        </div>
        <div class="info-row">
          <span class="label">VC Hash:</span>
          <span id="vcHash" class="value hash">-</span>
        </div>
      </div>
    </div>

    <!-- NFT Information -->
    <div class="info-card">
      <h3><i class="fas fa-gem"></i> NFT Information</h3>
      <div class="info-row">
        <span class="label">Token ID:</span>
        <span id="nftTokenId" class="value">-</span>
      </div>
      <div class="info-row">
        <span class="label">Token URI:</span>
        <span id="nftTokenURI" class="value link">-</span>
      </div>
    </div>

    <!-- Document Versions -->
    <div class="section-card">
      <h3><i class="fas fa-history"></i> Document Versions</h3>
      <div id="versions" class="versions-list">
        <div class="loading-placeholder">Loading versions...</div>
      </div>
    </div>

    <!-- Ownership History -->
    <div class="section-card">
      <h3><i class="fas fa-users"></i> Ownership History</h3>
      <div id="ownershipHistory" class="history-list">
        <div class="loading-placeholder">Loading ownership history...</div>
      </div>
    </div>

    <!-- Signers & Status -->
    <div class="section-card">
      <h3><i class="fas fa-user-check"></i> Approved Signers & Status</h3>
      <div id="signersStatusList" class="signers-list">
        <div class="loading-placeholder">Loading signers...</div>
      </div>
    </div>

    <!-- Expiry Management -->
    <div class="section-card">
      <h3><i class="fas fa-calendar-alt"></i> Expiry Management</h3>
      <div class="expiry-info">
        <div class="info-row">
          <span class="label">Current Expiry:</span>
          <span id="expiryDate" class="value">Loading...</span>
        </div>
        <div id="expiryUpdateDiv" class="expiry-update" style="display:none;">
          <div class="input-group">
            <input type="datetime-local" id="newExpiryInput" class="datetime-input" />
            <button id="setExpiryButton" class="btn-primary">
              <i class="fas fa-save"></i> Update Expiry
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Endorsements -->
    <div class="section-card">
      <h3><i class="fas fa-certificate"></i> Endorsements</h3>
      <div id="endorsements" class="endorsements-list">
        <div class="loading-placeholder">Loading endorsements...</div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<script>
// Contract addresses - loaded from server configuration
let bdioCoreAddress = null;
let accessControlAddress = null;
let endorsementAddress = null;
let vcManagerAddress = null;
let expiryManagerAddress = null;

// Load contract configuration
async function loadConfig() {
  try {
    const response = await fetch('/api/config');
    if (response.ok) {
      const config = await response.json();
      bdioCoreAddress = config.address;
      accessControlAddress = config.accessControl;
      endorsementAddress = config.endorsement;
      vcManagerAddress = config.vcManager;
      expiryManagerAddress = config.expiry;
      console.log('✅ Contract configuration loaded');
      return true;
    }
  } catch (error) {
    console.warn('⚠️ Using fallback addresses:', error.message);
    // Fallback addresses
    bdioCoreAddress = '******************************************';
    accessControlAddress = '******************************************';
    endorsementAddress = '******************************************';
    vcManagerAddress = '******************************************';
    expiryManagerAddress = '******************************************';
    return false;
  }
}

let provider, signer;
let bdioCore, accessControl, endorsementManager, vcManager, expiryManager;
let ownershipListenerAdded = false;
let currentUserAddress = null;

async function init() {
  try {
    // Load contract configuration first
    await loadConfig();

    if (!window.ethereum) {
      showError('MetaMask not detected. Please install MetaMask to use this application.');
      return;
    }

    provider = new ethers.BrowserProvider(window.ethereum);
    signer = await provider.getSigner();
    currentUserAddress = await signer.getAddress();

    // Load contract ABIs
    const [bdioAbi, accessAbi, endAbi, vcAbi, expiryAbi] = await Promise.all([
      loadABI('BDIOCoreRegistry'),
      loadABI('AccessControlManager'),
      loadABI('EndorsementManager'),
      loadABI('VCManager'),
      loadABI('BDIOExpiryManager')
    ]);

    // Initialize contracts
    bdioCore = new ethers.Contract(bdioCoreAddress, bdioAbi, provider);
    accessControl = new ethers.Contract(accessControlAddress, accessAbi, provider);
    endorsementManager = new ethers.Contract(endorsementAddress, endAbi, provider);
    vcManager = new ethers.Contract(vcManagerAddress, vcAbi, provider);
    expiryManager = new ethers.Contract(expiryManagerAddress, expiryAbi, provider);

    // Set up event listeners
    if (!ownershipListenerAdded) {
      bdioCore.on('OwnershipTransferred', async (bdioId) => {
        console.log('OwnershipTransferred:', bdioId);
        await updateMetadata(bdioId);
        if (document.getElementById('bdioIdInput').value.trim() === bdioId) {
          loadDocument();
        }
      });
      ownershipListenerAdded = true;
    }

    // Check for BDIO ID in URL parameters
    const param = new URLSearchParams(location.search).get('bdioId');
    if (param) {
      document.getElementById('bdioIdInput').value = param;
      loadDocument();
    }

  } catch (error) {
    console.error('Initialization error:', error);
    showError('Failed to initialize application: ' + error.message);
  }
}

async function loadABI(contractName) {
  try {
    const response = await fetch(`/abis/${contractName}.json`);
    if (!response.ok) {
      throw new Error(`Failed to load ${contractName} ABI`);
    }
    const data = await response.json();
    return data.abi || data; // Handle different ABI formats
  } catch (error) {
    console.error(`Error loading ${contractName} ABI:`, error);
    // Return minimal ABI for basic functionality
    return getMinimalABI(contractName);
  }
}

function getMinimalABI(contractName) {
  // Minimal ABIs for basic functionality when files are not available
  const minimalABIs = {
    'BDIOCoreRegistry': [
      "function verifyDocument(string) view returns (address, bool, bool, uint256, uint256, string, string)",
      "function getDocumentVersions(string) view returns (tuple(string,uint256,string,address,string)[])",
      "function getOwnershipHistory(string) view returns (tuple(address,uint256,string)[])",
      "function bdioToTokenId(string) view returns (uint256)",
      "function tokenURI(uint256) view returns (string)"
    ],
    'AccessControlManager': [
      "function getApprovedSigners(string) view returns (address[])",
      "function isSignerApproved(string, address) view returns (bool)",
      "function isAdmin(string, address) view returns (bool)"
    ],
    'EndorsementManager': [
      "function getEndorsementsCount(string) view returns (uint256)",
      "function getEndorsementByIndex(string, uint256) view returns (address, bytes, uint256, string)"
    ],
    'VCManager': [
      "function vcHash(string) view returns (string)"
    ],
    'BDIOExpiryManager': [
      "function getExpiry(string) view returns (uint256)",
      "function setExpiry(string, uint256)"
    ]
  };
  return minimalABIs[contractName] || [];
}

async function loadDocument() {
  const bdioId = document.getElementById('bdioIdInput').value.trim();
  if (!bdioId) {
    showError('Please enter a BDIO ID');
    return;
  }

  // Show loading state
  document.getElementById('loadingSpinner').style.display = 'block';
  document.getElementById('loadButton').disabled = true;
  document.getElementById('documentInfo').style.display = 'none';

  try {
    // Verify document exists and get basic info
    const [owner, active, archived, timestamp, versionCount, category, metadataUri] = await bdioCore.verifyDocument(bdioId);

    // Update status banner
    updateStatusBanner(active, archived);

    // Update basic document information
    document.getElementById('title').textContent = 'Loading...';
    document.getElementById('category').textContent = category || 'N/A';
    document.getElementById('owner').textContent = formatAddress(owner);
    document.getElementById('createdAt').textContent = formatDate(Number(timestamp) * 1000);

    // Load metadata
    const metaUrl = `/metadata/${bdioId}.meta.json`;
    document.getElementById('metadataUri').innerHTML = `<a href="${metaUrl}" target="_blank" class="external-link"><i class="fas fa-external-link-alt"></i> View Metadata</a>`;

    let meta = {};
    try {
      const response = await fetch(metaUrl);
      if (response.ok) {
        meta = await response.json();
      }
    } catch (error) {
      console.warn('Could not load metadata:', error);
    }

    // Update document details
    document.getElementById('title').textContent = meta.title || 'Untitled Document';
    document.getElementById('initialHash').textContent = formatHash(meta.hashHex || 'N/A');

    if (meta.blockchaintxHash) {
      document.getElementById('blockchainTxHash').innerHTML =
        `<a href="https://polygonscan.com/tx/${meta.blockchaintxHash}" target="_blank" class="external-link">
          <i class="fas fa-external-link-alt"></i> ${formatHash(meta.blockchaintxHash)}
        </a>`;
    } else {
      document.getElementById('blockchainTxHash').textContent = 'N/A';
    }

    // Load additional contract data in parallel
    await Promise.all([
      loadVCHash(bdioId),
      loadExpiryInfo(bdioId),
      loadVersions(bdioId),
      loadOwnershipHistory(bdioId),
      loadSignersStatus(bdioId),
      loadEndorsements(bdioId),
      loadNFTInfo(bdioId)
    ]);

    // Show expiry management if user is document owner
    const isOwner = owner.toLowerCase() === currentUserAddress.toLowerCase();
    document.getElementById('expiryUpdateDiv').style.display = isOwner ? 'block' : 'none';

    if (isOwner) {
      setupExpiryUpdate(bdioId);
    }

    document.getElementById('documentInfo').style.display = 'block';

  } catch (error) {
    console.error('Error loading document:', error);
    showError('Failed to load document: ' + (error.message || error));
  } finally {
    document.getElementById('loadingSpinner').style.display = 'none';
    document.getElementById('loadButton').disabled = false;
  }
}

// Helper functions for loading specific sections
async function loadVCHash(bdioId) {
  try {
    const vcHash = await vcManager.vcHash(bdioId);
    document.getElementById('vcHash').textContent = formatHash(vcHash || 'N/A');
  } catch (error) {
    console.error('Error loading VC hash:', error);
    document.getElementById('vcHash').textContent = 'Error loading';
  }
}

async function loadExpiryInfo(bdioId) {
  try {
    const expiry = await expiryManager.getExpiry(bdioId);
    const expiryElement = document.getElementById('expiryDate');
    const expiryStatusElement = document.getElementById('expiryStatus');

    if (expiry > 0) {
      const expiryDate = new Date(Number(expiry) * 1000);
      const isExpired = expiryDate < new Date();

      expiryElement.textContent = formatDate(expiryDate.getTime());
      expiryStatusElement.innerHTML = isExpired ?
        '<i class="fas fa-exclamation-triangle"></i> Expired' :
        '<i class="fas fa-clock"></i> ' + formatDate(expiryDate.getTime());
      expiryStatusElement.className = 'status-item ' + (isExpired ? 'expired' : 'valid');
    } else {
      expiryElement.textContent = 'No expiry set';
      expiryStatusElement.innerHTML = '<i class="fas fa-infinity"></i> No Expiry';
      expiryStatusElement.className = 'status-item valid';
    }
  } catch (error) {
    console.error('Error loading expiry:', error);
    document.getElementById('expiryDate').textContent = 'Error loading';
  }
}

async function loadVersions(bdioId) {
  try {
    const versions = await bdioCore.getDocumentVersions(bdioId);
    const versionsContainer = document.getElementById('versions');

    if (versions.length === 0) {
      versionsContainer.innerHTML = '<div class="empty-state">No versions found</div>';
      return;
    }

    versionsContainer.innerHTML = versions.map((version, index) => `
      <div class="version-item">
        <div class="version-header">
          <span class="version-number">v${versions.length - index}</span>
          <span class="version-date">${formatDate(Number(version.timestamp) * 1000)}</span>
        </div>
        <div class="version-details">
          <div class="version-hash">${formatHash(version.hashHex)}</div>
          <div class="version-editor">by ${formatAddress(version.editor)}</div>
          <div class="version-note">${version.note || 'No note'}</div>
        </div>
      </div>
    `).join('');
  } catch (error) {
    console.error('Error loading versions:', error);
    document.getElementById('versions').innerHTML = '<div class="error-state">Error loading versions</div>';
  }
}

async function loadOwnershipHistory(bdioId) {
  try {
    const history = await bdioCore.getOwnershipHistory(bdioId);
    const historyContainer = document.getElementById('ownershipHistory');

    if (history.length === 0) {
      historyContainer.innerHTML = '<div class="empty-state">No ownership history found</div>';
      return;
    }

    historyContainer.innerHTML = history.map((entry, index) => `
      <div class="history-item">
        <div class="history-header">
          <span class="history-index">#${history.length - index}</span>
          <span class="history-date">${formatDate(Number(entry.timestamp) * 1000)}</span>
        </div>
        <div class="history-details">
          <div class="history-owner">${formatAddress(entry.owner)}</div>
          <div class="history-note">${entry.note || 'No note'}</div>
        </div>
      </div>
    `).join('');
  } catch (error) {
    console.error('Error loading ownership history:', error);
    document.getElementById('ownershipHistory').innerHTML = '<div class="error-state">Error loading history</div>';
  }
}

async function loadSignersStatus(bdioId) {
  try {
    const approved = await accessControl.getApprovedSigners(bdioId);
    const signersContainer = document.getElementById('signersStatusList');

    if (approved.length === 0) {
      signersContainer.innerHTML = '<div class="empty-state">No approved signers</div>';
      return;
    }

    // Get endorsement data to check who has signed
    const endorsementCount = await endorsementManager.getEndorsementsCount(bdioId);
    const signed = new Set();

    for (let i = 0; i < endorsementCount; i++) {
      try {
        const endorsement = await endorsementManager.getEndorsementByIndex(bdioId, i);
        signed.add(endorsement[0].toLowerCase());
      } catch (error) {
        console.warn('Error loading endorsement:', error);
      }
    }

    const signerItems = await Promise.all(approved.map(async (signer) => {
      try {
        const isApproved = await accessControl.isSignerApproved(bdioId, signer);
        const hasSigned = signed.has(signer.toLowerCase());

        let status, statusClass;
        if (!isApproved) {
          status = 'Revoked';
          statusClass = 'revoked';
        } else if (hasSigned) {
          status = 'Signed';
          statusClass = 'signed';
        } else {
          status = 'Awaiting Signature';
          statusClass = 'pending';
        }

        return `
          <div class="signer-item">
            <div class="signer-address">${formatAddress(signer)}</div>
            <div class="signer-status ${statusClass}">
              <i class="fas ${getStatusIcon(statusClass)}"></i>
              ${status}
            </div>
          </div>
        `;
      } catch (error) {
        console.error('Error checking signer status:', error);
        return `
          <div class="signer-item">
            <div class="signer-address">${formatAddress(signer)}</div>
            <div class="signer-status error">Error</div>
          </div>
        `;
      }
    }));

    signersContainer.innerHTML = signerItems.join('');
  } catch (error) {
    console.error('Error loading signers:', error);
    document.getElementById('signersStatusList').innerHTML = '<div class="error-state">Error loading signers</div>';
  }
}

async function loadEndorsements(bdioId) {
  try {
    const count = await endorsementManager.getEndorsementsCount(bdioId);
    const endorsementsContainer = document.getElementById('endorsements');

    if (count === 0) {
      endorsementsContainer.innerHTML = '<div class="empty-state">No endorsements yet</div>';
      return;
    }

    const endorsements = [];
    for (let i = 0; i < count; i++) {
      try {
        const endorsement = await endorsementManager.getEndorsementByIndex(bdioId, i);
        endorsements.push({
          signer: endorsement[0],
          signature: endorsement[1],
          timestamp: Number(endorsement[2]),
          note: endorsement[3]
        });
      } catch (error) {
        console.warn('Error loading endorsement:', error);
      }
    }

    endorsementsContainer.innerHTML = endorsements.map((endorsement, index) => `
      <div class="endorsement-item">
        <div class="endorsement-header">
          <span class="endorsement-index">#${index + 1}</span>
          <span class="endorsement-date">${formatDate(endorsement.timestamp * 1000)}</span>
        </div>
        <div class="endorsement-details">
          <div class="endorsement-signer">${formatAddress(endorsement.signer)}</div>
          <div class="endorsement-note">${endorsement.note || 'No note provided'}</div>
        </div>
      </div>
    `).join('');
  } catch (error) {
    console.error('Error loading endorsements:', error);
    document.getElementById('endorsements').innerHTML = '<div class="error-state">Error loading endorsements</div>';
  }
}

async function loadNFTInfo(bdioId) {
  try {
    const tokenId = await bdioCore.bdioToTokenId(bdioId);

    if (tokenId && tokenId != 0) {
      document.getElementById('nftTokenId').textContent = tokenId.toString();
      try {
        const uri = await bdioCore.tokenURI(tokenId);
        document.getElementById('nftTokenURI').innerHTML =
          `<a href="${uri}" target="_blank" class="external-link">
            <i class="fas fa-external-link-alt"></i> View NFT Metadata
          </a>`;
      } catch (error) {
        document.getElementById('nftTokenURI').textContent = 'Error loading URI';
      }
    } else {
      document.getElementById('nftTokenId').textContent = 'No NFT minted';
      document.getElementById('nftTokenURI').textContent = 'N/A';
    }
  } catch (error) {
    console.error('Error loading NFT info:', error);
    document.getElementById('nftTokenId').textContent = 'Error';
    document.getElementById('nftTokenURI').textContent = 'Error';
  }
}

function setupExpiryUpdate(bdioId) {
  document.getElementById('setExpiryButton').onclick = async () => {
    const newExpiryInput = document.getElementById('newExpiryInput');
    const newExpiry = newExpiryInput.value;

    if (!newExpiry) {
      showError('Please select a date and time');
      return;
    }

    const expiryTimestamp = Math.floor(new Date(newExpiry).getTime() / 1000);

    try {
      showLoading('Updating expiry...');
      const tx = await expiryManager.connect(signer).setExpiry(bdioId, expiryTimestamp);
      await tx.wait();

      await updateMetadata(bdioId);
      showSuccess('Expiry updated successfully');
      loadDocument(); // Reload to show updated info
    } catch (error) {
      console.error('Error updating expiry:', error);
      showError('Failed to update expiry: ' + error.message);
    } finally {
      hideLoading();
    }
  };
}

// Utility functions
function updateStatusBanner(active, archived) {
  const statusIcon = document.getElementById('statusIcon');
  const statusText = document.getElementById('statusText');

  if (archived) {
    statusIcon.className = 'fas fa-archive';
    statusText.textContent = 'Archived';
    statusIcon.style.color = '#6c757d';
  } else if (active) {
    statusIcon.className = 'fas fa-circle';
    statusText.textContent = 'Active';
    statusIcon.style.color = '#28a745';
  } else {
    statusIcon.className = 'fas fa-pause-circle';
    statusText.textContent = 'Inactive';
    statusIcon.style.color = '#ffc107';
  }
}

function formatAddress(address) {
  if (!address || address === '0x0000000000000000000000000000000000000000') {
    return 'N/A';
  }
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
}

function formatHash(hash) {
  if (!hash || hash === 'N/A') return 'N/A';
  return `${hash.slice(0, 8)}...${hash.slice(-8)}`;
}

function formatDate(timestamp) {
  return new Date(timestamp).toLocaleString();
}

function getStatusIcon(statusClass) {
  switch (statusClass) {
    case 'signed': return 'fa-check-circle';
    case 'pending': return 'fa-clock';
    case 'revoked': return 'fa-times-circle';
    default: return 'fa-question-circle';
  }
}

async function updateMetadata(bdioId) {
  try {
    await fetch('/api/updateMetadata', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ bdioId })
    });
  } catch (error) {
    console.warn('Failed to update metadata:', error);
  }
}

function showError(message) {
  // You can implement a toast notification system here
  alert('Error: ' + message);
}

function showSuccess(message) {
  // You can implement a toast notification system here
  alert('Success: ' + message);
}

function showLoading(message) {
  // You can implement a loading overlay here
  console.log('Loading:', message);
}

function hideLoading() {
  // Hide loading overlay
  console.log('Loading complete');
}

// Initialize when page loads
window.onload = init;
</script>
</body>
</html>
