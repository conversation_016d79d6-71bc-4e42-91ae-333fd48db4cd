<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Document Dashboard - creden.xyz</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<link rel="stylesheet" href="style.css">




</head>
<body>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1><i class="fas fa-shield-alt"></i> creden.xyz</h1>
      <p>Blockchain Document Identity & Verification Platform</p>
      <div class="header-actions">
        <button id="connectBtn" class="btn btn-primary" onclick="connect()">
          <i class="fas fa-wallet"></i> Connect Wallet
        </button>
        <a href="register.html" class="btn btn-success">
          <i class="fas fa-plus-circle"></i> Register Document
        </a>
        <a href="nft.html" class="btn btn-outline">
          <i class="fas fa-gem"></i> Manage NFTs
        </a>
      </div>
    </div>

    <div class="main-content">
      <!-- Wallet Information -->
      <div id="walletSection" class="wallet-section">
        <div class="wallet-info">
          <div class="wallet-details">
            <h3><i class="fas fa-wallet"></i> Wallet Connected</h3>
            <div class="wallet-address" id="userAddress">Not connected</div>
          </div>
          <button class="btn btn-outline" onclick="refreshData()">
            <i class="fas fa-sync-alt"></i> Refresh Data
          </button>
        </div>
      </div>

      <!-- Statistics -->
      <div class="stats-grid">
        <div class="stat-card">
          <i class="fas fa-file-alt"></i>
          <div class="stat-number" id="totalDocsCount">0</div>
          <div class="stat-label">Total Documents</div>
        </div>
        <div class="stat-card">
          <i class="fas fa-user-check"></i>
          <div class="stat-number" id="ownedDocsCount">0</div>
          <div class="stat-label">Owned Documents</div>
        </div>
        <div class="stat-card">
          <i class="fas fa-signature"></i>
          <div class="stat-number" id="pendingSignCount">0</div>
          <div class="stat-label">Pending Signatures</div>
        </div>
        <div class="stat-card">
          <i class="fas fa-gem"></i>
          <div class="stat-number" id="nftCount">0</div>
          <div class="stat-label">NFTs Minted</div>
        </div>
      </div>

      <!-- Status Display -->
      <div id="status" class="status-container">
        <div class="status-title">
          <i class="fas fa-info-circle"></i> Status
        </div>
        <div class="status-content">Please connect your wallet to view your documents.</div>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions">
        <a href="register.html" class="btn btn-success">
          <i class="fas fa-plus-circle"></i> Register New Document
        </a>
        <a href="verify.html" class="btn btn-primary">
          <i class="fas fa-search"></i> Verify Document
        </a>
        <a href="nft.html" class="btn btn-outline">
          <i class="fas fa-gem"></i> NFT Manager
        </a>
      </div>

      <!-- Your Documents Section -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-folder-open"></i> Your Documents
          </h2>
          <button class="btn btn-outline" onclick="loadDocuments()" id="refreshOwnedBtn">
            <i class="fas fa-sync-alt"></i> Refresh
          </button>
        </div>
        <div class="table-container">
          <table class="table" id="ownedTable" style="display:none;">
            <thead>
              <tr>
                <th><i class="fas fa-fingerprint"></i> BDIO ID</th>
                <th><i class="fas fa-file-alt"></i> Title</th>
                <th><i class="fas fa-tags"></i> Category</th>
                <th><i class="fas fa-info-circle"></i> Status</th>
                <th><i class="fas fa-gem"></i> NFT</th>
                <th><i class="fas fa-cog"></i> Actions</th>
              </tr>
            </thead>
            <tbody id="ownedBody"></tbody>
          </table>
          <div id="ownedEmpty" class="empty-state">
            <i class="fas fa-folder-open"></i>
            <h3>No Documents Found</h3>
            <p>You don't own any documents yet. Register your first document to get started.</p>
            <a href="register.html" class="btn btn-success" style="margin-top: 20px;">
              <i class="fas fa-plus-circle"></i> Register Document
            </a>
          </div>
        </div>
      </div>

      <!-- Documents to Sign Section -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-signature"></i> Documents Awaiting Your Signature
          </h2>
          <button class="btn btn-outline" onclick="loadDocuments()" id="refreshApprovedBtn">
            <i class="fas fa-sync-alt"></i> Refresh
          </button>
        </div>
        <div class="table-container">
          <table class="table" id="approvedTable" style="display:none;">
            <thead>
              <tr>
                <th><i class="fas fa-fingerprint"></i> BDIO ID</th>
                <th><i class="fas fa-file-alt"></i> Title</th>
                <th><i class="fas fa-tags"></i> Category</th>
                <th><i class="fas fa-user"></i> Owner</th>
                <th><i class="fas fa-check-circle"></i> Signed</th>
                <th><i class="fas fa-cog"></i> Actions</th>
              </tr>
            </thead>
            <tbody id="approvedBody"></tbody>
          </table>
          <div id="approvedEmpty" class="empty-state">
            <i class="fas fa-signature"></i>
            <h3>No Pending Signatures</h3>
            <p>You don't have any documents waiting for your signature.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

<script>
// Contract Configuration
const CONTRACT_ADDRESS = 'YOUR_CONTRACT_ADDRESS_HERE'; // Replace with actual deployed contract address
const NETWORK_CONFIG = {
  chainId: "0x89", // Polygon Mainnet
  chainName: "Polygon Mainnet",
  rpcUrls: ["https://polygon-rpc.com/"],
  nativeCurrency: {
    name: "POL",
    symbol: "POL",
    decimals: 18
  },
  blockExplorerUrls: ["https://polygonscan.com/"]
};

// Contract ABI (simplified for dashboard functions)
const CONTRACT_ABI = [
  "function totalSupply() external view returns (uint256)",
  "function tokenByIndex(uint256 index) external view returns (uint256)",
  "function tokenIdToBdio(uint256 tokenId) external view returns (string)",
  "function bdioToTokenId(string bdioId) external view returns (uint256)",
  "function exists(string bdioId) external view returns (bool)",
  "function getDocumentOwner(string bdioId) external view returns (address)",
  "function verifyDocument(string bdioId) external view returns (address owner, bool active, bool archived, uint256 timestamp, uint versionCount, string category, string metadataUri)",
  "function getOwnershipHistory(string bdioId) external view returns (tuple(address owner, uint256 timestamp, string note)[])",
  "function getDocumentVersions(string bdioId) external view returns (tuple(string hashHex, uint256 timestamp, string note, address editor, string txHash)[])",
  "function contractOwner() external view returns (address)",
  "event DocumentRegistered(string indexed bdioId, address indexed owner)",
  "event OwnershipTransferred(string indexed bdioId, address indexed oldOwner, address indexed newOwner)"
];

// Global variables
let provider;
let signer;
let contract;
let currentAccount;
let documentsData = {
  owned: [],
  approved: [],
  stats: {
    total: 0,
    owned: 0,
    pending: 0,
    nfts: 0
  }
};

// Initialize the application
window.addEventListener('load', async () => {
  await initializeApp();
});

async function initializeApp() {
  try {
    if (typeof window.ethereum !== 'undefined') {
      provider = new ethers.BrowserProvider(window.ethereum);

      // Check if already connected
      const accounts = await window.ethereum.request({ method: 'eth_accounts' });
      if (accounts.length > 0) {
        await connect();
      } else {
        log("Please connect your wallet to view your documents.", 'info');
      }
    } else {
      log("❌ MetaMask not detected. Please install MetaMask to use this application.", 'error');
    }
  } catch (error) {
    console.error("Initialization error:", error);
    log("❌ Failed to initialize application: " + error.message, 'error');
  }
}

async function connect() {
  try {
    setLoading(true);
    log("🔗 Connecting to wallet...", 'info');

    const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
    if (accounts.length > 0) {
      currentAccount = accounts[0];
      signer = await provider.getSigner();
      contract = new ethers.Contract(CONTRACT_ADDRESS, CONTRACT_ABI, signer);

      // Update UI
      document.getElementById('userAddress').textContent =
        currentAccount.substring(0, 6) + '...' + currentAccount.substring(38);
      document.getElementById('walletSection').classList.add('show');
      document.getElementById('connectBtn').style.display = 'none';

      log("✅ Wallet connected successfully. Loading your documents...", 'success');

      // Load user documents
      await loadDocuments();
    }
  } catch (error) {
    console.error("Wallet connection error:", error);
    log("❌ Failed to connect wallet: " + error.message, 'error');
  } finally {
    setLoading(false);
  }
}

async function refreshData() {
  if (!currentAccount) {
    log("❌ Please connect your wallet first.", 'error');
    return;
  }

  setLoading(true);
  log("🔄 Refreshing data...", 'info');
  await loadDocuments();
  setLoading(false);
}

function setLoading(isLoading) {
  const refreshBtns = document.querySelectorAll('#refreshOwnedBtn, #refreshApprovedBtn, button[onclick="refreshData()"]');
  refreshBtns.forEach(btn => {
    if (isLoading) {
      btn.disabled = true;
      const icon = btn.querySelector('i');
      if (icon) {
        icon.className = 'fas fa-spinner fa-spin';
      }
    } else {
      btn.disabled = false;
      const icon = btn.querySelector('i');
      if (icon) {
        icon.className = 'fas fa-sync-alt';
      }
    }
  });
}

async function loadDocuments() {
  try {
    if (!contract || !currentAccount) {
      log("❌ Please connect your wallet first.", 'error');
      return;
    }

    log("📊 Loading documents and statistics...", 'info');

    // Reset data
    documentsData = {
      owned: [],
      approved: [],
      stats: { total: 0, owned: 0, pending: 0, nfts: 0 }
    };

    // Get all documents by iterating through registered documents
    // Note: Since the new contract doesn't have totalSupply/tokenByIndex for all documents,
    // we'll need to use events or a different approach. For now, let's simulate with a reasonable approach.

    // Try to get total supply (this might not work with the new contract structure)
    let totalDocs = 0;
    try {
      // If the contract has ERC721Enumerable, this will work
      totalDocs = await contract.totalSupply();
    } catch (e) {
      // If not, we'll need to use a different approach
      console.warn("totalSupply not available, using alternative method");
      totalDocs = 0;
    }

    documentsData.stats.total = Number(totalDocs);

    // If we have totalSupply, iterate through tokens
    if (totalDocs > 0) {
      for (let i = 0; i < totalDocs; i++) {
        try {
          const tokenId = await contract.tokenByIndex(i);
          const bdioId = await contract.tokenIdToBdio(tokenId);
          await processDocument(bdioId);
        } catch (e) {
          console.warn(`Error processing token ${i}:`, e);
        }
      }
    }

    // For demonstration, let's also check some common BDIO IDs
    // In a real implementation, you might want to use events or a backend service
    await checkCommonDocuments();

    // Update statistics
    updateStatistics();

    // Render tables
    renderOwnedDocuments();
    renderApprovedDocuments();

    log(`✅ Loaded ${documentsData.owned.length} owned documents and ${documentsData.approved.length} pending signatures.`, 'success');

  } catch (error) {
    console.error("Error loading documents:", error);
    log("❌ Failed to load documents: " + error.message, 'error');
  }
}

async function processDocument(bdioId) {
  try {
    // Check if document exists
    const exists = await contract.exists(bdioId);
    if (!exists) return;

    // Get document details
    const [owner, active, archived, timestamp, versionCount, category, metadataUri] =
      await contract.verifyDocument(bdioId);

    // Get ownership history
    const history = await contract.getOwnershipHistory(bdioId);

    // Check if user is current owner
    const isCurrentOwner = owner.toLowerCase() === currentAccount.toLowerCase();

    // Check if user was ever an owner
    const wasOwner = history.some(h => h.owner.toLowerCase() === currentAccount.toLowerCase());

    // Get NFT status
    const tokenId = await contract.bdioToTokenId(bdioId);
    const hasNFT = tokenId.toString() !== "0";

    // Try to get title from metadata (if available)
    let title = `Document ${bdioId.substring(0, 8)}...`;
    try {
      if (metadataUri && metadataUri !== "") {
        const response = await fetch(metadataUri);
        if (response.ok) {
          const metadata = await response.json();
          title = metadata.title || title;
        }
      }
    } catch (e) {
      // Ignore metadata fetch errors
    }

    const documentData = {
      bdioId,
      title,
      category: category || 'Uncategorized',
      owner,
      active,
      archived,
      timestamp: Number(timestamp),
      versionCount: Number(versionCount),
      hasNFT,
      tokenId: hasNFT ? tokenId.toString() : null
    };

    // Add to owned documents if user is/was owner
    if (isCurrentOwner || wasOwner) {
      documentsData.owned.push({
        ...documentData,
        status: isCurrentOwner ? 'Owner' : 'Former Owner',
        isCurrentOwner
      });

      if (isCurrentOwner) {
        documentsData.stats.owned++;
      }

      if (hasNFT) {
        documentsData.stats.nfts++;
      }
    }

    // Note: For approved documents (endorsement system), we would need
    // the AccessControlManager and EndorsementManager contracts
    // This is simplified for the core registry functionality

  } catch (error) {
    console.warn(`Error processing document ${bdioId}:`, error);
  }
}

async function checkCommonDocuments() {
  // This is a placeholder for checking documents that might not be in the NFT enumeration
  // In a real implementation, you might:
  // 1. Use event logs to find documents
  // 2. Have a backend service that indexes documents
  // 3. Use a subgraph for querying

  // For now, we'll skip this to avoid errors
  console.log("Checking for additional documents...");
}

function updateStatistics() {
  document.getElementById('totalDocsCount').textContent = documentsData.stats.total;
  document.getElementById('ownedDocsCount').textContent = documentsData.stats.owned;
  document.getElementById('pendingSignCount').textContent = documentsData.stats.pending;
  document.getElementById('nftCount').textContent = documentsData.stats.nfts;
}

function renderOwnedDocuments() {
  const table = document.getElementById('ownedTable');
  const tbody = document.getElementById('ownedBody');
  const emptyState = document.getElementById('ownedEmpty');

  tbody.innerHTML = '';

  if (documentsData.owned.length === 0) {
    table.style.display = 'none';
    emptyState.style.display = 'block';
    return;
  }

  table.style.display = 'table';
  emptyState.style.display = 'none';

  documentsData.owned.forEach(doc => {
    const row = document.createElement('tr');

    // Status badge
    let statusClass = 'status-active';
    let statusText = doc.status;
    if (doc.archived) {
      statusClass = 'status-archived';
      statusText = 'Archived';
    } else if (!doc.active) {
      statusClass = 'status-inactive';
      statusText = 'Inactive';
    }

    // NFT status
    const nftStatus = doc.hasNFT
      ? `<span class="status-badge status-signed">NFT #${doc.tokenId}</span>`
      : `<span class="status-badge status-inactive">No NFT</span>`;

    row.innerHTML = `
      <td><span class="bdio-id">${doc.bdioId}</span></td>
      <td>${doc.title}</td>
      <td>${doc.category}</td>
      <td><span class="status-badge ${statusClass}">${statusText}</span></td>
      <td>${nftStatus}</td>
      <td>
        <a href="get.html?bdioId=${encodeURIComponent(doc.bdioId)}" class="btn btn-outline" style="margin-right: 5px; padding: 6px 12px; font-size: 0.8rem;">
          <i class="fas fa-eye"></i> View
        </a>
        ${doc.isCurrentOwner ? `
          <a href="nft.html" class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">
            <i class="fas fa-gem"></i> NFT
          </a>
        ` : ''}
      </td>
    `;

    tbody.appendChild(row);
  });
}

function renderApprovedDocuments() {
  const table = document.getElementById('approvedTable');
  const tbody = document.getElementById('approvedBody');
  const emptyState = document.getElementById('approvedEmpty');

  tbody.innerHTML = '';

  if (documentsData.approved.length === 0) {
    table.style.display = 'none';
    emptyState.style.display = 'block';
    return;
  }

  table.style.display = 'table';
  emptyState.style.display = 'none';

  documentsData.approved.forEach(doc => {
    const row = document.createElement('tr');

    const signedStatus = doc.signed
      ? `<span class="status-badge status-signed">Signed</span>`
      : `<span class="status-badge status-inactive">Pending</span>`;

    row.innerHTML = `
      <td><span class="bdio-id">${doc.bdioId}</span></td>
      <td>${doc.title}</td>
      <td>${doc.category}</td>
      <td>${doc.owner.substring(0, 6)}...${doc.owner.substring(38)}</td>
      <td>${signedStatus}</td>
      <td>
        ${doc.signed
          ? `<button class="btn" disabled style="padding: 6px 12px; font-size: 0.8rem;">
               <i class="fas fa-check"></i> Signed
             </button>`
          : `<a href="endorse.html?bdioId=${encodeURIComponent(doc.bdioId)}" class="btn btn-success" style="padding: 6px 12px; font-size: 0.8rem;">
               <i class="fas fa-signature"></i> Sign
             </a>`
        }
        <a href="get.html?bdioId=${encodeURIComponent(doc.bdioId)}" class="btn btn-outline" style="margin-left: 5px; padding: 6px 12px; font-size: 0.8rem;">
          <i class="fas fa-eye"></i> View
        </a>
      </td>
    `;

    tbody.appendChild(row);
  });
}

function formatDate(timestamp) {
  return new Date(timestamp * 1000).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

function log(msg, type = 'info') {
  const statusContainer = document.getElementById("status");
  const statusContent = statusContainer.querySelector('.status-content');

  statusContent.textContent = msg;

  // Remove existing status classes
  statusContainer.classList.remove('error', 'success');

  // Add appropriate class based on type
  if (type === 'error') {
    statusContainer.classList.add('error');
  } else if (type === 'success') {
    statusContainer.classList.add('success');
  }
}

// Handle wallet account changes
if (window.ethereum) {
  window.ethereum.on('accountsChanged', function (accounts) {
    if (accounts.length === 0) {
      // User disconnected wallet
      location.reload();
    } else if (accounts[0] !== currentAccount) {
      // User switched accounts
      location.reload();
    }
  });

  window.ethereum.on('chainChanged', function (chainId) {
    // User switched networks
    location.reload();
  });
}
</script>
</body>
</html>
