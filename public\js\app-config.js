// BDIO Registry Frontend Configuration
// This file gets contract addresses from the server endpoint

const BDIO_CONFIG = {
  // Application Settings
  SETTINGS: {
    // File upload limits
    MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
    
    // Supported file types
    SUPPORTED_FILE_TYPES: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/jpg',
      'image/png'
    ],
    
    // BDIO ID validation
    MIN_BDIO_ID_LENGTH: 10,
    MAX_BDIO_ID_LENGTH: 64,
    
    // Note validation
    MAX_NOTE_LENGTH: 256,
    
    // Hash validation
    HASH_LENGTH: 66, // 0x + 64 hex characters
    
    // UI Settings
    LOG_MAX_ENTRIES: 100,
    ANIMATION_DURATION: 200
  },

  // Network Configuration
  NETWORKS: {
    POLYGON_MAINNET: {
      chainId: 137,
      name: 'Polygon Mainnet',
      rpcUrl: 'https://polygon-rpc.com',
      blockExplorer: 'https://polygonscan.com',
      nativeCurrency: {
        name: 'POL',
        symbol: 'POL',
        decimals: 18
      }
    },
    POLYGON_TESTNET: {
      chainId: 80001,
      name: 'Polygon Mumbai',
      rpcUrl: 'https://rpc-mumbai.maticvigil.com',
      blockExplorer: 'https://mumbai.polygonscan.com',
      nativeCurrency: {
        name: 'POL',
        symbol: 'POL',
        decimals: 18
      }
    }
  },

  // Default network
  DEFAULT_NETWORK: 'POLYGON_MAINNET',

  // Default fees (will be overridden by contract values)
  DEFAULT_FEES: {
    REGISTER_FEE: '0.2',
    MINT_FEE: '0.5',
    VERSION_FEE: '0.2'
  },

  // Contract addresses - will be loaded from server
  CONTRACTS: {
    BDIO_CORE_REGISTRY: null,
    ACCESS_CONTROL_MANAGER: null,
    ENDORSEMENT_MANAGER: null,
    VC_MANAGER: null,
    EXPIRY_MANAGER: null
  }
};

// Function to load contract configuration from server
async function loadContractConfig() {
  try {
    const response = await fetch('/api/config');
    if (response.ok) {
      const config = await response.json();
      
      // Update contract addresses
      BDIO_CONFIG.CONTRACTS.BDIO_CORE_REGISTRY = config.address;
      BDIO_CONFIG.CONTRACTS.ACCESS_CONTROL_MANAGER = config.accessControl;
      BDIO_CONFIG.CONTRACTS.ENDORSEMENT_MANAGER = config.endorsement;
      BDIO_CONFIG.CONTRACTS.VC_MANAGER = config.vcManager;
      BDIO_CONFIG.CONTRACTS.EXPIRY_MANAGER = config.expiry;
      
      console.log('✅ Contract configuration loaded from server');
      return config;
    } else {
      console.warn('⚠️ Could not load config from server, using fallback');
      return null;
    }
  } catch (error) {
    console.warn('⚠️ Error loading config from server:', error.message);
    return null;
  }
}

// Fallback contract addresses (from .env file values)
const FALLBACK_CONTRACTS = {
  BDIO_CORE_REGISTRY: '0xC954fD7aC0cAeaD6BF54e5d9d8123b8D5E87FE6f',
  ACCESS_CONTROL_MANAGER: '0x1abfAE2B9b67B30EC092B0ca3D895495C6D32129',
  ENDORSEMENT_MANAGER: '0xd87a5d71bb96E10f057B3E15464F1bEe5Bf71ECf',
  VC_MANAGER: '0xea506d6455ad80B7Ec38966f6AE228ea429f2ffA',
  EXPIRY_MANAGER: '0x1D9431053b113cBEE4166C61bb2668d04085d0C9'
};

// Initialize configuration
async function initializeConfig() {
  const serverConfig = await loadContractConfig();
  
  if (!serverConfig) {
    // Use fallback addresses
    BDIO_CONFIG.CONTRACTS = { ...FALLBACK_CONTRACTS };
    console.log('📋 Using fallback contract addresses');
  }
  
  return BDIO_CONFIG;
}

// Make available globally
window.BDIO_CONFIG = BDIO_CONFIG;
window.initializeConfig = initializeConfig;

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { BDIO_CONFIG, initializeConfig };
}
