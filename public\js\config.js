// BDIO Registry Configuration
// Update this file when deploying new contract versions

const BDIO_CONFIG = {
  // Contract Addresses - UPDATE THESE WHEN DEPLOYING NEW CONTRACTS
  CONTRACTS: {
    // Main registry contract
    BDIO_CORE_REGISTRY: '0xCB7A6426daB27c649a9947007a14B1CD0aBb0aDe',
    
    // Additional contracts (if needed)
    ACCESS_CONTROL_MANAGER: '',
    ENDORSEMENT_MANAGER: '',
    VC_MANAGER: '',
    EXPIRY_MANAGER: ''
  },

  // Network Configuration
  NETWORKS: {
    POLYGON_MAINNET: {
      chainId: 137,
      name: 'Polygon Mainnet',
      rpcUrl: 'https://polygon-rpc.com',
      blockExplorer: 'https://polygonscan.com',
      nativeCurrency: {
        name: 'MA<PERSON><PERSON>',
        symbol: 'MATIC',
        decimals: 18
      }
    },
    POLYGON_TESTNET: {
      chainId: 80001,
      name: 'Polygon Mumbai',
      rpcUrl: 'https://rpc-mumbai.maticvigil.com',
      blockExplorer: 'https://mumbai.polygonscan.com',
      nativeCurrency: {
        name: 'MATIC',
        symbol: 'MATIC',
        decimals: 18
      }
    }
  },

  // Default network (change this based on deployment)
  DEFAULT_NETWORK: 'POLYGON_TESTNET',

  // Application Settings
  SETTINGS: {
    // File upload limits
    MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
    
    // Supported file types
    SUPPORTED_FILE_TYPES: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/jpg',
      'image/png'
    ],
    
    // BDIO ID validation
    MIN_BDIO_ID_LENGTH: 10,
    MAX_BDIO_ID_LENGTH: 64,
    
    // Note validation
    MAX_NOTE_LENGTH: 256,
    
    // Hash validation
    HASH_LENGTH: 66, // 0x + 64 hex characters
    
    // UI Settings
    LOG_MAX_ENTRIES: 100,
    ANIMATION_DURATION: 200
  },

  // Default fees (will be overridden by contract values)
  DEFAULT_FEES: {
    REGISTER_FEE: '0.2',
    MINT_FEE: '0.5',
    VERSION_FEE: '0.2'
  },

  // API Endpoints (if using backend services)
  API: {
    METADATA_BASE_URL: 'https://creden.xyz/metadata/',
    IPFS_GATEWAY: 'https://ipfs.io/ipfs/'
  }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = BDIO_CONFIG;
}

// Make available globally
window.BDIO_CONFIG = BDIO_CONFIG;
