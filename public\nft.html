<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>NFT Manager - creden.xyz</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
  }

  .container {
    max-width: 900px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
  }

  .header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 300;
  }

  .header p {
    opacity: 0.9;
    font-size: 1.1rem;
  }

  .main-content {
    padding: 40px;
  }

  .wallet-info {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
    border: 1px solid #4caf50;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    display: none;
  }

  .wallet-info.show {
    display: block;
  }

  .wallet-info h3 {
    color: #2e7d32;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .wallet-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
  }

  .wallet-detail:last-child {
    margin-bottom: 0;
  }

  .wallet-detail strong {
    color: #2e7d32;
  }

  .wallet-detail span {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #666;
  }

  .refresh-btn {
    background: #4caf50;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
  }

  .refresh-btn:hover {
    background: #45a049;
    transform: translateY(-1px);
  }

  .action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
  }

  .action-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    border: 2px solid #e1e8ed;
    transition: all 0.3s ease;
  }

  .action-card:hover {
    border-color: #667eea;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
  }

  .action-card h3 {
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.3rem;
  }

  .action-card h3 i {
    color: #667eea;
    font-size: 1.5rem;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
  }

  .required {
    color: #e74c3c;
  }

  .form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
  }

  .form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  .btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }

  .btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  }

  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .btn-danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  }

  .btn-danger:hover:not(:disabled) {
    box-shadow: 0 8px 20px rgba(231, 76, 60, 0.3);
  }

  .btn-success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  }

  .btn-success:hover:not(:disabled) {
    box-shadow: 0 8px 20px rgba(39, 174, 96, 0.3);
  }

  .status-container {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    border-left: 4px solid #667eea;
  }

  .status-title {
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .status-content {
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #666;
    line-height: 1.6;
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
  }

  .query-result {
    margin-top: 15px;
    padding: 15px;
    background: #e8f5e8;
    border: 1px solid #4caf50;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    display: none;
  }

  .query-result.show {
    display: block;
  }

  .query-result a {
    color: #667eea;
    text-decoration: none;
    word-break: break-all;
  }

  .query-result a:hover {
    text-decoration: underline;
  }

  .loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  .error {
    color: #e74c3c;
    background: #fdf2f2;
    border: 1px solid #e74c3c;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
  }

  .success {
    color: #27ae60;
    background: #f0f9f0;
    border: 1px solid #27ae60;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
  }

  .fee-info {
    background: #fff3cd;
    border: 1px solid #ffc107;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  @media (max-width: 768px) {
    .container {
      margin: 10px;
      border-radius: 15px;
    }

    .main-content {
      padding: 20px;
    }

    .header {
      padding: 20px;
    }

    .header h1 {
      font-size: 2rem;
    }

    .action-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .action-card {
      padding: 20px;
    }
  }
</style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1><i class="fas fa-gem"></i> NFT Manager</h1>
      <p>Mint, Burn & Manage Document NFTs</p>
    </div>

    <div class="main-content">
      <!-- Wallet Connection Status -->
      <div id="walletInfo" class="wallet-info">
        <h3><i class="fas fa-wallet"></i> Wallet Information</h3>
        <div class="wallet-detail">
          <strong>Connected Wallet:</strong>
          <span id="walletAddress">Not connected</span>
        </div>
        <div class="wallet-detail">
          <strong>Contract Owner:</strong>
          <span id="ownerAddress">Loading...</span>
          <button class="refresh-btn" onclick="loadOwner()">
            <i class="fas fa-sync-alt"></i> Refresh
          </button>
        </div>
      </div>

      <!-- Action Cards Grid -->
      <div class="action-grid">
        <!-- Transfer Ownership Card -->
        <div class="action-card">
          <h3><i class="fas fa-exchange-alt"></i> Transfer Ownership</h3>
          <div class="form-group">
            <label for="transferBdioId">BDIO ID <span class="required">*</span></label>
            <input type="text" id="transferBdioId" class="form-control" placeholder="Enter BDIO ID" maxlength="64" />
          </div>
          <div class="form-group">
            <label for="newOwner">New Owner Address <span class="required">*</span></label>
            <input type="text" id="newOwner" class="form-control" placeholder="0x..." maxlength="42" />
          </div>
          <div class="form-group">
            <label for="transferNote">Transfer Note</label>
            <input type="text" id="transferNote" class="form-control" placeholder="Enter note for transfer" maxlength="256" />
          </div>
          <button class="btn" onclick="transferOwnership()">
            <i class="fas fa-exchange-alt"></i>
            <span id="transferBtnText">Transfer Ownership</span>
          </button>
        </div>

        <!-- Mint NFT Card -->
        <div class="action-card">
          <h3><i class="fas fa-plus-circle"></i> Mint NFT</h3>
          <div class="fee-info">
            <i class="fas fa-info-circle"></i>
            <span>Mint Fee: <span id="mintFeeDisplay">Loading...</span></span>
          </div>
          <div class="form-group">
            <label for="mintBdioId">BDIO ID <span class="required">*</span></label>
            <input type="text" id="mintBdioId" class="form-control" placeholder="Enter BDIO ID" maxlength="64" />
          </div>
          <button class="btn btn-success" onclick="mintNFT()">
            <i class="fas fa-plus-circle"></i>
            <span id="mintBtnText">Mint NFT</span>
          </button>
        </div>

        <!-- Burn NFT Card -->
        <div class="action-card">
          <h3><i class="fas fa-fire"></i> Burn NFT</h3>
          <div class="form-group">
            <label for="burnBdioId">BDIO ID <span class="required">*</span></label>
            <input type="text" id="burnBdioId" class="form-control" placeholder="Enter BDIO ID" maxlength="64" />
          </div>
          <button class="btn btn-danger" onclick="burnNFT()">
            <i class="fas fa-fire"></i>
            <span id="burnBtnText">Burn NFT</span>
          </button>
        </div>

        <!-- Query Token Card -->
        <div class="action-card">
          <h3><i class="fas fa-search"></i> Query Token Info</h3>
          <div class="form-group">
            <label for="queryBdioId">BDIO ID <span class="required">*</span></label>
            <input type="text" id="queryBdioId" class="form-control" placeholder="Enter BDIO ID" maxlength="64" />
          </div>
          <button class="btn" onclick="queryTokenIdAndURI()">
            <i class="fas fa-search"></i>
            <span id="queryBtnText">Get Token Info</span>
          </button>
          <div id="queryResult" class="query-result"></div>
        </div>
      </div>

      <!-- Status Display -->
      <div class="status-container">
        <div class="status-title">
          <i class="fas fa-info-circle"></i> Status
        </div>
        <div id="status" class="status-content">Ready to manage NFTs. Please connect your wallet to get started.</div>
      </div>
    </div>
  </div>

<script>
// Application Configuration
const APP_CONFIG = {
  MINT_FEE: "0.5", // 0.5 POL per NFT mint
  MAX_BDIO_ID_LENGTH: 64,
  MIN_BDIO_ID_LENGTH: 10
};

const NETWORK_CONFIG = {
  chainId: "0x89", // Polygon Mainnet
  chainName: "Polygon Mainnet",
  rpcUrls: ["https://polygon-rpc.com/"],
  nativeCurrency: {
    name: "POL",
    symbol: "POL",
    decimals: 18
  },
  blockExplorerUrls: ["https://polygonscan.com/"]
};

// Contract address - will be loaded from server
let CONTRACT_ADDRESS = null;

// Load contract configuration from server
async function loadContractConfig() {
  try {
    const response = await fetch('/api/config');
    if (response.ok) {
      const config = await response.json();
      CONTRACT_ADDRESS = config.address;
      console.log('✅ Contract loaded:', CONTRACT_ADDRESS);
      return true;
    } else {
      throw new Error('Server config not available');
    }
  } catch (error) {
    console.warn('⚠️ Using fallback contract address');
    CONTRACT_ADDRESS = '0xC954fD7aC0cAeaD6BF54e5d9d8123b8D5E87FE6f'; // Fallback from .env
    return false;
  }
}

// Contract ABI (simplified for NFT functions)
const CONTRACT_ABI = [
  "function mintNFT(string bdioId) external payable",
  "function burnNFT(string bdioId) external",
  "function transferOwnershipManual(string bdioId, address newOwner, string note) external",
  "function bdioToTokenId(string bdioId) external view returns (uint256)",
  "function tokenIdToBdio(uint256 tokenId) external view returns (string)",
  "function tokenURI(uint256 tokenId) external view returns (string)",
  "function contractOwner() external view returns (address)",
  "function mintFee() external view returns (uint256)",
  "function exists(string bdioId) external view returns (bool)",
  "function getDocumentOwner(string bdioId) external view returns (address)",
  "function verifyDocument(string bdioId) external view returns (address owner, bool active, bool archived, uint256 timestamp, uint versionCount, string category, string metadataUri)",
  "event DocumentRegistered(string indexed bdioId, address indexed owner)",
  "event OwnershipTransferred(string indexed bdioId, address indexed oldOwner, address indexed newOwner)"
];

// Global variables
let provider;
let signer;
let contract;
let currentAccount;

// Initialize the application
window.addEventListener('load', async () => {
  await initializeApp();
});

async function initializeApp() {
  try {
    // Load contract configuration first
    log("🚀 Loading contract configuration...");
    await loadContractConfig();

    if (!CONTRACT_ADDRESS) {
      log("❌ Contract address not available. Please check server configuration.", 'error');
      return;
    }

    log(`📋 Using contract: ${CONTRACT_ADDRESS}`);

    if (typeof window.ethereum !== 'undefined') {
      provider = new ethers.BrowserProvider(window.ethereum);
      await connectWallet();
    } else {
      log("❌ MetaMask not detected. Please install MetaMask to use this application.", 'error');
    }
  } catch (error) {
    console.error("Initialization error:", error);
    log("❌ Failed to initialize application: " + error.message, 'error');
  }
}

async function connectWallet() {
  try {
    const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
    if (accounts.length > 0) {
      currentAccount = accounts[0];
      signer = await provider.getSigner();
      contract = new ethers.Contract(CONTRACT_ADDRESS, CONTRACT_ABI, signer);

      // Update UI
      document.getElementById('walletAddress').textContent =
        currentAccount.substring(0, 6) + '...' + currentAccount.substring(38);
      document.getElementById('walletInfo').classList.add('show');

      // Load fees and owner info
      await loadContractInfo();

      log("✅ Wallet connected successfully. Ready to manage NFTs.", 'success');
    }
  } catch (error) {
    console.error("Wallet connection error:", error);
    log("❌ Failed to connect wallet: " + error.message, 'error');
  }
}

async function loadContractInfo() {
  try {
    // Load contract owner
    await loadOwner();

    // Load mint fee
    try {
      const mintFee = await contract.mintFee();
      const feeInEther = ethers.formatEther(mintFee);
      document.getElementById('mintFeeDisplay').textContent = `${feeInEther} POL`;
      log(`💰 Mint fee: ${feeInEther} POL`);
    } catch (e) {
      console.warn("Could not fetch mint fee from contract, using default");
      document.getElementById('mintFeeDisplay').textContent = `${APP_CONFIG.MINT_FEE} POL (fallback)`;
      log(`💰 Using fallback mint fee: ${APP_CONFIG.MINT_FEE} POL`);
    }
  } catch (error) {
    console.error("Error loading contract info:", error);
  }
}

async function loadOwner() {
  try {
    const owner = await contract.contractOwner();
    document.getElementById('ownerAddress').textContent =
      owner.substring(0, 6) + '...' + owner.substring(38);
  } catch (e) {
    console.error("Failed to load owner:", e);
    log('❌ Failed to load contract owner: ' + e.message, 'error');
  }
}

function validateInputs(bdioId, additionalFields = {}) {
  if (!bdioId || bdioId.trim() === '') {
    log('❌ BDIO ID is required.', 'error');
    return false;
  }

  if (bdioId.length < 10 || bdioId.length > 64) {
    log('❌ BDIO ID must be between 10 and 64 characters.', 'error');
    return false;
  }

  // Validate additional fields
  for (const [field, value] of Object.entries(additionalFields)) {
    if (field === 'newOwner' && !ethers.isAddress(value)) {
      log('❌ Invalid Ethereum address format.', 'error');
      return false;
    }
  }

  return true;
}

function setLoading(buttonId, isLoading, originalText) {
  const btn = document.querySelector(`button[onclick="${buttonId}()"]`);
  const btnText = document.getElementById(`${buttonId}BtnText`);

  if (isLoading) {
    btn.disabled = true;
    btnText.innerHTML = '<div class="loading"></div> Processing...';
  } else {
    btn.disabled = false;
    btnText.textContent = originalText;
  }
}

async function transferOwnership() {
  try {
    const bdioId = document.getElementById('transferBdioId').value.trim();
    const newOwner = document.getElementById('newOwner').value.trim();
    const note = document.getElementById('transferNote').value.trim() || 'Manual transfer';

    if (!validateInputs(bdioId, { newOwner })) return;

    setLoading('transferOwnership', true, 'Transfer Ownership');
    log('🔍 Checking document ownership...');

    // Check if document exists and get current owner
    const exists = await contract.exists(bdioId);
    if (!exists) {
      log(`❌ Document with BDIO ID "${bdioId}" does not exist.`, 'error');
      return;
    }

    const currentOwner = await contract.getDocumentOwner(bdioId);
    if (currentOwner.toLowerCase() !== currentAccount.toLowerCase()) {
      log(`❌ You are not the owner of this document. Current owner: ${currentOwner}`, 'error');
      return;
    }

    if (currentOwner.toLowerCase() === newOwner.toLowerCase()) {
      log('❌ Cannot transfer to the same address.', 'error');
      return;
    }

    log('📡 Submitting ownership transfer...');
    const tx = await contract.transferOwnershipManual(bdioId, newOwner, note);

    log('⏳ Waiting for blockchain confirmation...');
    const receipt = await tx.wait();

    const successMsg = `✅ Ownership transferred successfully!

📄 BDIO ID: ${bdioId}
👤 New Owner: ${newOwner}
📝 Note: ${note}
🔗 Transaction: ${receipt.hash}
🌐 View on Explorer: https://polygonscan.com/tx/${receipt.hash}`;

    log(successMsg, 'success');

    // Clear form
    document.getElementById('transferBdioId').value = '';
    document.getElementById('newOwner').value = '';
    document.getElementById('transferNote').value = '';

    // Update metadata if API is available
    await updateMetadata(bdioId);

  } catch (error) {
    console.error("Transfer ownership error:", error);
    let errorMsg = "❌ Ownership transfer failed: ";

    if (error.code === 'INSUFFICIENT_FUNDS') {
      errorMsg += "Insufficient funds for transaction.";
    } else if (error.code === 'USER_REJECTED') {
      errorMsg += "Transaction was rejected by user.";
    } else if (error.message.includes('Not document owner')) {
      errorMsg += "You are not the owner of this document.";
    } else {
      errorMsg += error.message || "Unknown error occurred.";
    }

    log(errorMsg, 'error');
  } finally {
    setLoading('transferOwnership', false, 'Transfer Ownership');
  }
}

async function mintNFT() {
  try {
    const bdioId = document.getElementById('mintBdioId').value.trim();

    if (!validateInputs(bdioId)) return;

    setLoading('mintNFT', true, 'Mint NFT');
    log('🔍 Checking document and NFT status...');

    // Check if document exists
    const exists = await contract.exists(bdioId);
    if (!exists) {
      log(`❌ Document with BDIO ID "${bdioId}" does not exist.`, 'error');
      return;
    }

    // Check if user owns the document
    const documentOwner = await contract.getDocumentOwner(bdioId);
    if (documentOwner.toLowerCase() !== currentAccount.toLowerCase()) {
      log(`❌ You are not the owner of this document. Owner: ${documentOwner}`, 'error');
      return;
    }

    // Check if NFT already exists
    const existingTokenId = await contract.bdioToTokenId(bdioId);
    if (existingTokenId.toString() !== "0") {
      log(`❌ NFT already exists for this document. Token ID: ${existingTokenId.toString()}`, 'error');
      return;
    }

    // Get mint fee
    const mintFee = await contract.mintFee();

    log('📡 Submitting NFT mint transaction...');
    const tx = await contract.mintNFT(bdioId, { value: mintFee });

    log('⏳ Waiting for blockchain confirmation...');
    const receipt = await tx.wait();

    // Get the new token ID
    const tokenIdRaw = await contract.bdioToTokenId(bdioId);
    const tokenId = tokenIdRaw.toString();

    const successMsg = `✅ NFT minted successfully!

📄 BDIO ID: ${bdioId}
🎫 Token ID: ${tokenId}
💰 Mint Fee: ${ethers.formatEther(mintFee)} POL
🔗 Transaction: ${receipt.hash}
🌐 View on Explorer: https://polygonscan.com/tx/${receipt.hash}`;

    log(successMsg, 'success');

    // Clear form
    document.getElementById('mintBdioId').value = '';

    // Try to save NFT metadata to server (optional)
    try {
      await saveNFTMetadata(bdioId, tokenId);
    } catch (e) {
      console.warn("Failed to save NFT metadata to server:", e);
    }

    // Update BDIO metadata
    await updateMetadata(bdioId);

  } catch (error) {
    console.error("Mint NFT error:", error);
    let errorMsg = "❌ NFT minting failed: ";

    if (error.code === 'INSUFFICIENT_FUNDS') {
      errorMsg += "Insufficient funds for minting fee.";
    } else if (error.code === 'USER_REJECTED') {
      errorMsg += "Transaction was rejected by user.";
    } else if (error.message.includes('Not document owner')) {
      errorMsg += "You are not the owner of this document.";
    } else if (error.message.includes('Already minted')) {
      errorMsg += "NFT already exists for this document.";
    } else {
      errorMsg += error.message || "Unknown error occurred.";
    }

    log(errorMsg, 'error');
  } finally {
    setLoading('mintNFT', false, 'Mint NFT');
  }
}

async function saveNFTMetadata(bdioId, tokenId) {
  try {
    const response = await fetch('/api/saveNft', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        bdioId,
        tokenId,
        contract: CONTRACT_ADDRESS,
        name: `BDIO Document #${bdioId}`,
        description: 'Blockchain Document Identity Object (BDIO) NFT - Immutable proof of document authenticity and ownership.',
        image: `${window.location.origin}/assets/nft.png`,
        external_url: `${window.location.origin}/get?bdioId=${bdioId}`,
        attributes: [
          { trait_type: 'BDIO ID', value: bdioId },
          { trait_type: 'Token ID', value: tokenId },
          { trait_type: 'Contract', value: CONTRACT_ADDRESS },
          { trait_type: 'Minted At', value: new Date().toISOString() },
          { trait_type: 'Network', value: 'Polygon' }
        ]
      })
    });

    if (response.ok) {
      log('✅ NFT metadata saved to server.');
    } else {
      const errorText = await response.text();
      console.warn('Failed to save NFT metadata:', errorText);
    }
  } catch (error) {
    console.warn('Error saving NFT metadata:', error);
  }
}

async function burnNFT() {
  try {
    const bdioId = document.getElementById('burnBdioId').value.trim();

    if (!validateInputs(bdioId)) return;

    setLoading('burnNFT', true, 'Burn NFT');
    log('🔍 Checking NFT status...');

    // Check if document exists
    const exists = await contract.exists(bdioId);
    if (!exists) {
      log(`❌ Document with BDIO ID "${bdioId}" does not exist.`, 'error');
      return;
    }

    // Check if user owns the document
    const documentOwner = await contract.getDocumentOwner(bdioId);
    if (documentOwner.toLowerCase() !== currentAccount.toLowerCase()) {
      log(`❌ You are not the owner of this document. Owner: ${documentOwner}`, 'error');
      return;
    }

    // Check if NFT exists
    const tokenId = await contract.bdioToTokenId(bdioId);
    if (tokenId.toString() === "0") {
      log(`❌ No NFT exists for this document.`, 'error');
      return;
    }

    // Confirm burn action
    if (!confirm(`Are you sure you want to burn the NFT for BDIO ID "${bdioId}"? This action cannot be undone.`)) {
      log('🚫 Burn operation cancelled by user.');
      return;
    }

    log('🔥 Submitting NFT burn transaction...');
    const tx = await contract.burnNFT(bdioId);

    log('⏳ Waiting for blockchain confirmation...');
    const receipt = await tx.wait();

    const successMsg = `✅ NFT burned successfully!

📄 BDIO ID: ${bdioId}
🎫 Token ID: ${tokenId.toString()}
🔗 Transaction: ${receipt.hash}
🌐 View on Explorer: https://polygonscan.com/tx/${receipt.hash}`;

    log(successMsg, 'success');

    // Clear form
    document.getElementById('burnBdioId').value = '';

    // Update BDIO metadata
    await updateMetadata(bdioId);

  } catch (error) {
    console.error("Burn NFT error:", error);
    let errorMsg = "❌ NFT burning failed: ";

    if (error.code === 'USER_REJECTED') {
      errorMsg += "Transaction was rejected by user.";
    } else if (error.message.includes('Not document owner')) {
      errorMsg += "You are not the owner of this document.";
    } else if (error.message.includes('Not minted')) {
      errorMsg += "No NFT exists for this document.";
    } else {
      errorMsg += error.message || "Unknown error occurred.";
    }

    log(errorMsg, 'error');
  } finally {
    setLoading('burnNFT', false, 'Burn NFT');
  }
}

async function queryTokenIdAndURI() {
  try {
    const bdioId = document.getElementById('queryBdioId').value.trim();

    if (!validateInputs(bdioId)) return;

    setLoading('queryTokenIdAndURI', true, 'Get Token Info');
    log('🔍 Querying document and token information...');

    // Check if document exists
    const exists = await contract.exists(bdioId);
    if (!exists) {
      log(`❌ Document with BDIO ID "${bdioId}" does not exist.`, 'error');
      document.getElementById('queryResult').classList.remove('show');
      return;
    }

    // Get document info
    const [owner, active, archived, timestamp, versionCount, category, metadataUri] =
      await contract.verifyDocument(bdioId);

    // Get token info
    const tokenIdRaw = await contract.bdioToTokenId(bdioId);
    const tokenId = tokenIdRaw.toString();

    let resultHTML = `
      <h4><i class="fas fa-file-alt"></i> Document Information</h4>
      <p><strong>BDIO ID:</strong> ${bdioId}</p>
      <p><strong>Owner:</strong> ${owner}</p>
      <p><strong>Status:</strong> ${active ? '✅ Active' : '❌ Inactive'} ${archived ? '(Archived)' : ''}</p>
      <p><strong>Category:</strong> ${category || 'Not specified'}</p>
      <p><strong>Versions:</strong> ${versionCount.toString()}</p>
      <p><strong>Created:</strong> ${new Date(Number(timestamp) * 1000).toLocaleString()}</p>
    `;

    if (tokenId !== "0") {
      try {
        const uri = await contract.tokenURI(tokenId);
        resultHTML += `
          <hr style="margin: 15px 0; border: none; border-top: 1px solid #ddd;">
          <h4><i class="fas fa-gem"></i> NFT Information</h4>
          <p><strong>Token ID:</strong> ${tokenId}</p>
          <p><strong>Token URI:</strong> <a href="${uri}" target="_blank">${uri}</a></p>
          <p><strong>OpenSea:</strong> <a href="https://opensea.io/assets/matic/${CONTRACT_ADDRESS}/${tokenId}" target="_blank">View on OpenSea</a></p>
        `;
        log('✅ Document and NFT information loaded successfully.', 'success');
      } catch (e) {
        resultHTML += `
          <hr style="margin: 15px 0; border: none; border-top: 1px solid #ddd;">
          <h4><i class="fas fa-gem"></i> NFT Information</h4>
          <p><strong>Token ID:</strong> ${tokenId}</p>
          <p><strong>Status:</strong> ⚠️ Error loading token URI</p>
        `;
        log('✅ Document information loaded. NFT URI could not be retrieved.', 'success');
      }
    } else {
      resultHTML += `
        <hr style="margin: 15px 0; border: none; border-top: 1px solid #ddd;">
        <h4><i class="fas fa-gem"></i> NFT Information</h4>
        <p><strong>Status:</strong> 🚫 No NFT minted for this document</p>
        <p><em>You can mint an NFT for this document using the Mint NFT section above.</em></p>
      `;
      log('✅ Document information loaded. No NFT exists for this document.', 'success');
    }

    document.getElementById('queryResult').innerHTML = resultHTML;
    document.getElementById('queryResult').classList.add('show');

  } catch (error) {
    console.error("Query error:", error);
    log('❌ Failed to query token information: ' + (error.message || error), 'error');
    document.getElementById('queryResult').classList.remove('show');
  } finally {
    setLoading('queryTokenIdAndURI', false, 'Get Token Info');
  }
}

async function updateMetadata(bdioId) {
  try {
    const response = await fetch('/api/updateMetadata', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ bdioId })
    });

    if (response.ok) {
      console.log('✅ BDIO metadata updated on server.');
    } else {
      console.warn('⚠️ Failed to update BDIO metadata on server.');
    }
  } catch (error) {
    console.warn('❌ Error updating metadata:', error.message);
  }
}

function log(msg, type = 'info') {
  const statusElement = document.getElementById("status");
  statusElement.textContent = msg;

  // Remove existing status classes
  statusElement.classList.remove('error', 'success');

  // Add appropriate class based on type
  if (type === 'error') {
    statusElement.classList.add('error');
  } else if (type === 'success') {
    statusElement.classList.add('success');
  }
}

// Initialize when page loads
window.addEventListener('load', initializeApp);
</script>

</body>
</html>
