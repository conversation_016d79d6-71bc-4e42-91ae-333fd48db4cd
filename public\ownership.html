<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>🔄 Transfer Ownership - BDIO Registry</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<link rel="stylesheet" href="style.css">
<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
  }

  .container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
  }

  .header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 300;
  }

  .header p {
    opacity: 0.9;
    font-size: 1.1rem;
  }

  .form-container {
    padding: 40px;
  }

  .form-group {
    margin-bottom: 25px;
  }

  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
  }

  .required {
    color: #e74c3c;
  }

  .form-control {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
  }

  .form-control:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  textarea.form-control {
    min-height: 100px;
    resize: vertical;
  }

  .btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 18px 30px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
  }

  .btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }

  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .btn-success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  }

  .btn-success:hover:not(:disabled) {
    box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
  }

  .btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
  }

  .btn-outline:hover:not(:disabled) {
    background: #667eea;
    color: white;
  }

  .wallet-info {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
    border: 1px solid #4caf50;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .wallet-info i {
    color: #4caf50;
  }

  .warning-info {
    background: #fff3cd;
    border: 1px solid #ffc107;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
  }

  .warning-info i {
    color: #856404;
    margin-top: 2px;
  }

  .status-container {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
  }

  .status-title {
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
  }

  .status-content {
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #666;
    line-height: 1.5;
    max-height: 200px;
    overflow-y: auto;
  }

  .ownership-info {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
  }

  .ownership-info h4 {
    color: #1976d2;
    margin-bottom: 10px;
  }

  .ownership-info .current-owner {
    font-family: 'Courier New', monospace;
    background: white;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #bbdefb;
    margin-top: 10px;
  }

  @media (max-width: 768px) {
    .container {
      margin: 10px;
      border-radius: 15px;
    }

    .form-container {
      padding: 20px;
    }

    .header {
      padding: 20px;
    }

    .header h1 {
      font-size: 2rem;
    }
  }
</style>
</head>
<body>
<div class="container">
  <div class="header">
    <h1><i class="fas fa-exchange-alt"></i> Transfer Ownership</h1>
    <p>Transfer document ownership to another address</p>
  </div>
  <div class="form-container">
    <!-- Wallet Connection Status -->
    <div id="walletInfo" class="wallet-info">
      <i class="fas fa-wallet"></i> <strong>Wallet Connected:</strong> <span id="walletAddress">Not connected</span>
    </div>

    <!-- Warning Information -->
    <div class="warning-info">
      <i class="fas fa-exclamation-triangle"></i>
      <div>
        <strong>Important:</strong> Ownership transfer is permanent and cannot be undone.
        Make sure you trust the recipient address and double-check it before proceeding.
      </div>
    </div>

    <!-- Current Ownership Info -->
    <div id="ownershipInfo" class="ownership-info" style="display: none;">
      <h4><i class="fas fa-info-circle"></i> Current Document Information</h4>
      <div><strong>Document ID:</strong> <span id="currentBdioId">-</span></div>
      <div><strong>Current Owner:</strong></div>
      <div class="current-owner" id="currentOwner">-</div>
    </div>

    <!-- Form Section -->
    <div class="form-group">
      <label for="bdioId">BDIO Document ID <span class="required">*</span></label>
      <input
        type="text"
        id="bdioId"
        class="form-control"
        placeholder="Enter BDIO ID (e.g., abc123...)"
        onblur="checkCurrentOwnership()"
      />
    </div>

    <div class="form-group">
      <label for="newOwner">New Owner Address <span class="required">*</span></label>
      <input
        type="text"
        id="newOwner"
        class="form-control"
        placeholder="Enter new owner Ethereum address (0x...)"
        style="font-family: 'Courier New', monospace;"
      />
    </div>

    <div class="form-group">
      <label for="transferNote">Transfer Note (Optional)</label>
      <textarea
        id="transferNote"
        class="form-control"
        rows="3"
        placeholder="Add a note about this ownership transfer..."
      ></textarea>
      <small style="color: #666; margin-top: 5px; display: block;">
        This note will be permanently stored on the blockchain
      </small>
    </div>

    <!-- Action Buttons -->
    <button onclick="transferOwnership()" class="btn btn-success" id="transferBtn">
      <i class="fas fa-exchange-alt"></i> Transfer Ownership
    </button>

    <button onclick="checkCurrentOwnership()" class="btn btn-outline">
      <i class="fas fa-search"></i> Check Current Owner
    </button>

    <!-- Status Section -->
    <div class="status-container">
      <div class="status-title">
        <i class="fas fa-info-circle"></i> Activity Log
      </div>
      <div id="status" class="status-content">
        Ready to transfer ownership. Please connect your wallet to get started.
      </div>
    </div>
  </div>
</div>

<script>
// Application Configuration
const APP_CONFIG = {
  MIN_BDIO_ID_LENGTH: 10,
  MAX_BDIO_ID_LENGTH: 64,
  MAX_NOTE_LENGTH: 500
};

// Contract addresses - will be loaded from server
let bdioCoreAddress = null;

// Load contract configuration
async function loadContractConfig() {
  try {
    const response = await fetch('/api/config');
    if (response.ok) {
      const config = await response.json();
      bdioCoreAddress = config.address;
      console.log('✅ Contract configuration loaded');
      return true;
    } else {
      throw new Error('Server config not available');
    }
  } catch (error) {
    console.warn('⚠️ Using fallback contract addresses');
    bdioCoreAddress = '******************************************';
    return false;
  }
}

// Complete ABI for the functions we need
const bdioAbi = [
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "newOwner", "type": "address"}, {"internalType": "string", "name": "note", "type": "string"}],
    "name": "transferOwnershipManual",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "verifyDocument",
    "outputs": [
      {"internalType": "address", "name": "owner", "type": "address"},
      {"internalType": "bool", "name": "active", "type": "bool"},
      {"internalType": "bool", "name": "archived", "type": "bool"},
      {"internalType": "uint256", "name": "timestamp", "type": "uint256"},
      {"internalType": "uint256", "name": "versionCount", "type": "uint256"},
      {"internalType": "string", "name": "category", "type": "string"},
      {"internalType": "string", "name": "metadataUri", "type": "string"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "exists",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "view",
    "type": "function"
  }
];

let provider, signer, contract;

// Initialize the application
async function init() {
  log('🚀 Initializing Ownership Transfer...');

  // Load contract configuration first
  await loadContractConfig();

  if (!bdioCoreAddress) {
    log('❌ Contract address not available. Please check server configuration.');
    return;
  }

  if (!window.ethereum) {
    log('❌ MetaMask not detected. Please install MetaMask extension.');
    updateConnectionStatus(false, 'MetaMask not found');
    return;
  }

  try {
    updateConnectionStatus('loading', 'Connecting...');
    log('🔗 Connecting to MetaMask...');

    provider = new ethers.BrowserProvider(window.ethereum);
    await provider.send("eth_requestAccounts", []);
    signer = await provider.getSigner();

    contract = new ethers.Contract(bdioCoreAddress, bdioAbi, signer);

    const address = await signer.getAddress();
    const network = await provider.getNetwork();

    log(`✅ Connected successfully!`);
    log(`👤 Wallet: ${address}`);
    log(`🌐 Network: ${network.name} (Chain ID: ${network.chainId})`);
    log(`📋 BDIO Registry: ${bdioCoreAddress}`);

    updateConnectionStatus(true, address);

  } catch (error) {
    console.error('Connection error:', error);
    log(`❌ Connection failed: ${error.message}`);
    updateConnectionStatus(false, 'Connection failed');
  }
}

// Update connection status indicator
function updateConnectionStatus(connected, address = '') {
  const walletInfo = document.getElementById('walletInfo');
  const addressEl = document.getElementById('walletAddress');

  if (connected === 'loading') {
    addressEl.textContent = 'Connecting...';
    walletInfo.style.background = 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)';
    walletInfo.style.borderColor = '#ffc107';
  } else if (connected) {
    addressEl.textContent = `${address.slice(0, 6)}...${address.slice(-4)}`;
    walletInfo.style.background = 'linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%)';
    walletInfo.style.borderColor = '#4caf50';
  } else {
    addressEl.textContent = address || 'Not connected';
    walletInfo.style.background = 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)';
    walletInfo.style.borderColor = '#dc3545';
  }
}

async function transferOwnership() {
  try {
    if (!contract) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    const bdioId = getInput('bdioId');
    const newOwner = getInput('newOwner');
    const note = document.getElementById('transferNote').value.trim() || 'Manual ownership transfer';

    log('🔍 Verifying document ownership...');

    // Verify document exists and get current owner
    const docInfo = await contract.verifyDocument(bdioId);
    const currentOwner = docInfo[0];
    const active = docInfo[1];
    const archived = docInfo[2];

    const myAddr = await signer.getAddress();

    log(`📄 Document found - Current Owner: ${currentOwner.slice(0, 6)}...${currentOwner.slice(-4)}`);
    log(`📊 Status: ${active ? 'Active' : 'Inactive'}, ${archived ? 'Archived' : 'Not archived'}`);

    // Check ownership
    if (myAddr.toLowerCase() !== currentOwner.toLowerCase()) {
      log('❌ Access denied: You are not the owner of this document.');
      return;
    }

    // Check if trying to transfer to same address
    if (currentOwner.toLowerCase() === newOwner.toLowerCase()) {
      log('❌ Cannot transfer to the same address.');
      return;
    }

    // Validate new owner address
    if (!ethers.isAddress(newOwner)) {
      log('❌ Invalid Ethereum address format.');
      return;
    }

    log('✅ Ownership verified. Preparing transfer...');
    log(`🔄 Transferring from: ${currentOwner}`);
    log(`🔄 Transferring to: ${newOwner}`);
    log(`📝 Note: "${note}"`);

    log('📡 Sending transferOwnershipManual transaction...');
    const tx = await contract.transferOwnershipManual(bdioId, newOwner, note);
    log(`⏳ Transaction sent: ${tx.hash}`);
    log('⏳ Waiting for confirmation...');

    const receipt = await tx.wait();

    log(`✅ Ownership transferred successfully!`);
    log(`📦 Block number: ${receipt.blockNumber}`);
    log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);
    log(`🔗 Transaction: ${tx.hash}`);
    log(`🎉 Document ${bdioId} is now owned by ${newOwner}`);

    // Clear form
    document.getElementById('bdioId').value = '';
    document.getElementById('newOwner').value = '';
    document.getElementById('transferNote').value = '';

    // Hide ownership info
    document.getElementById('ownershipInfo').style.display = 'none';

  } catch (error) {
    console.error('Transfer ownership error:', error);

    let errorMessage = 'Unknown error occurred';
    if (error.reason) {
      errorMessage = error.reason;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.data?.message) {
      errorMessage = error.data.message;
    }

    log(`❌ Transfer failed: ${errorMessage}`);

    // Handle specific errors
    if (errorMessage.includes('insufficient funds')) {
      log('💡 Tip: Make sure you have enough POL for the transaction fee');
    } else if (errorMessage.includes('user rejected')) {
      log('💡 Transaction was cancelled by user');
    } else if (errorMessage.includes('Not document owner')) {
      log('💡 You are not the owner of this document');
    } else if (errorMessage.includes('Document not found')) {
      log('💡 Please check the BDIO ID and try again');
    }
  }
}

async function checkCurrentOwnership() {
  try {
    const bdioId = document.getElementById('bdioId').value.trim();

    if (!bdioId || bdioId.length < APP_CONFIG.MIN_BDIO_ID_LENGTH) {
      document.getElementById('ownershipInfo').style.display = 'none';
      return;
    }

    if (!contract) {
      log('❌ Wallet not connected. Please connect your wallet first.');
      return;
    }

    log('🔍 Checking document ownership...');

    // Verify document exists
    const exists = await contract.exists(bdioId);
    if (!exists) {
      log('❌ Document not found with this BDIO ID');
      document.getElementById('ownershipInfo').style.display = 'none';
      return;
    }

    // Get document info
    const docInfo = await contract.verifyDocument(bdioId);
    const owner = docInfo[0];
    const active = docInfo[1];
    const archived = docInfo[2];
    const timestamp = docInfo[3];

    // Update ownership info display
    document.getElementById('currentBdioId').textContent = bdioId;
    document.getElementById('currentOwner').textContent = owner;
    document.getElementById('ownershipInfo').style.display = 'block';

    const registrationDate = new Date(Number(timestamp) * 1000);

    log(`✅ Document found!`);
    log(`📄 BDIO ID: ${bdioId}`);
    log(`👤 Owner: ${owner}`);
    log(`📊 Status: ${active ? 'Active' : 'Inactive'}, ${archived ? 'Archived' : 'Not archived'}`);
    log(`📅 Registered: ${registrationDate.toLocaleString()}`);

  } catch (error) {
    console.error('Check ownership error:', error);
    log(`❌ Error checking ownership: ${error.message}`);
    document.getElementById('ownershipInfo').style.display = 'none';
  }
}

function getInput(id) {
  const val = document.getElementById(id).value.trim();
  if (!val) {
    log(`❌ ${id} is required`);
    throw new Error(`${id} is required`);
  }

  // Validate BDIO ID length
  if (id === 'bdioId' && (val.length < APP_CONFIG.MIN_BDIO_ID_LENGTH || val.length > APP_CONFIG.MAX_BDIO_ID_LENGTH)) {
    log(`❌ BDIO ID must be between ${APP_CONFIG.MIN_BDIO_ID_LENGTH}-${APP_CONFIG.MAX_BDIO_ID_LENGTH} characters`);
    throw new Error('Invalid BDIO ID length');
  }

  // Validate Ethereum address format
  if (id === 'newOwner' && !ethers.isAddress(val)) {
    log(`❌ Invalid Ethereum address format`);
    throw new Error('Invalid address format');
  }

  return val;
}

function log(msg) {
  const el = document.getElementById('status');
  const timestamp = new Date().toLocaleTimeString();
  el.textContent += `[${timestamp}] ${msg}\n`;
  el.scrollTop = el.scrollHeight;
}

// Handle wallet account changes
if (window.ethereum) {
  window.ethereum.on('accountsChanged', (accounts) => {
    if (accounts.length === 0) {
      log('🔌 Wallet disconnected');
      updateConnectionStatus(false, 'Disconnected');
    } else {
      log('🔄 Account changed, reconnecting...');
      init();
    }
  });

  window.ethereum.on('chainChanged', (chainId) => {
    log(`🌐 Network changed to chain ID: ${chainId}`);
    log('🔄 Reconnecting...');
    window.location.reload();
  });
}

// Initialize when page loads
window.addEventListener('load', init);
</script>

</body>
</html>
