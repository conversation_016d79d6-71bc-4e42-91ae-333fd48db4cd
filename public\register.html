<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Register Document - creden.xyz</title>
    <script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        font-weight: 300;
      }

      .header p {
        opacity: 0.9;
        font-size: 1.1rem;
      }

      .form-container {
        padding: 40px;
      }

      .form-group {
        margin-bottom: 25px;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
        font-size: 0.95rem;
      }

      .required {
        color: #e74c3c;
      }

      .form-control {
        width: 100%;
        padding: 15px;
        border: 2px solid #e1e8ed;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8f9fa;
      }

      .form-control:focus {
        outline: none;
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .file-input-wrapper {
        position: relative;
        display: inline-block;
        width: 100%;
      }

      .file-input {
        position: absolute;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
      }

      .file-input-display {
        display: flex;
        align-items: center;
        padding: 15px;
        border: 2px dashed #e1e8ed;
        border-radius: 10px;
        background: #f8f9fa;
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .file-input-display:hover {
        border-color: #667eea;
        background: #f0f4ff;
      }

      .file-input-display i {
        margin-right: 10px;
        color: #667eea;
        font-size: 1.2rem;
      }

      .btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 18px 30px;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
      }

      .btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
      }

      .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      .status-container {
        margin-top: 30px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
        border-left: 4px solid #667eea;
      }

      .status-title {
        font-weight: 600;
        margin-bottom: 10px;
        color: #333;
      }

      .status-content {
        white-space: pre-wrap;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        color: #666;
        line-height: 1.5;
      }

      .mode-selector {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-bottom: 25px;
      }

      .mode-option {
        padding: 20px;
        border: 2px solid #e1e8ed;
        border-radius: 10px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #f8f9fa;
      }

      .mode-option.active {
        border-color: #667eea;
        background: #f0f4ff;
        color: #667eea;
      }

      .mode-option:hover {
        border-color: #667eea;
      }

      .mode-option i {
        font-size: 2rem;
        margin-bottom: 10px;
        display: block;
      }

      .wallet-info {
        background: #e8f5e8;
        border: 1px solid #4caf50;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 25px;
        display: none;
      }

      .wallet-info.show {
        display: block;
      }

      .fee-info {
        background: #fff3cd;
        border: 1px solid #ffc107;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 25px;
        font-size: 0.9rem;
      }

      .loading {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s ease-in-out infinite;
      }

      @keyframes spin {
        to { transform: rotate(360deg); }
      }

      .error {
        color: #e74c3c;
        background: #fdf2f2;
        border: 1px solid #e74c3c;
        padding: 15px;
        border-radius: 10px;
        margin-top: 15px;
      }

      .success {
        color: #27ae60;
        background: #f0f9f0;
        border: 1px solid #27ae60;
        padding: 15px;
        border-radius: 10px;
        margin-top: 15px;
      }

      @media (max-width: 768px) {
        .container {
          margin: 10px;
          border-radius: 15px;
        }

        .form-container {
          padding: 20px;
        }

        .header {
          padding: 20px;
        }

        .header h1 {
          font-size: 2rem;
        }

        .mode-selector {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1><i class="fas fa-certificate"></i> creden.xyz</h1>
        <p>Secure Document Registration on Blockchain</p>
      </div>

      <div class="form-container">
        <!-- Wallet Connection Status -->
        <div id="walletInfo" class="wallet-info">
          <i class="fas fa-wallet"></i> <strong>Wallet Connected:</strong> <span id="walletAddress"></span>
        </div>

        <!-- Registration Mode Selection -->
        <div class="form-group">
          <label>Registration Mode <span class="required">*</span></label>
          <div class="mode-selector">
            <div class="mode-option active" data-mode="single">
              <i class="fas fa-file"></i>
              <div><strong>Single Document</strong></div>
              <small>Register one document</small>
            </div>
            <div class="mode-option" data-mode="multiple">
              <i class="fas fa-files"></i>
              <div><strong>Multiple Documents</strong></div>
              <small>Batch register documents</small>
            </div>
          </div>
        </div>

        <!-- Fee Information -->
        <div class="fee-info">
          <i class="fas fa-info-circle"></i>
          <strong>Registration Fee:</strong> <span id="feeDisplay">Loading...</span>
        </div>

        <!-- Document Title -->
        <div class="form-group">
          <label for="title">Document Title <span class="required">*</span></label>
          <input type="text" id="title" class="form-control" placeholder="e.g. Training Certificate, Legal Contract" maxlength="256" />
        </div>

        <!-- Document Category -->
        <div class="form-group">
          <label for="category">Category</label>
          <input type="text" id="category" class="form-control" placeholder="e.g. Legal, HR, Academic, Certificate, Medical" maxlength="256" />
        </div>

        <!-- Metadata URI (Hidden for now) -->
        <input type="hidden" id="metadataUri" value="" />

        <!-- File Selection -->
        <div class="form-group">
          <label>Select File(s) <span class="required">*</span></label>
          <div class="file-input-wrapper">
            <input type="file" id="file" class="file-input" />
            <div class="file-input-display">
              <i class="fas fa-cloud-upload-alt"></i>
              <span id="fileDisplayText">Click to select file(s) or drag and drop</span>
            </div>
          </div>
          <small style="color: #666; margin-top: 5px; display: block;">
            Supported formats: PDF, DOC, DOCX, TXT, JPG, PNG (Max 10MB per file)
          </small>
        </div>

        <!-- Register Button -->
        <button id="btnRegister" class="btn" onclick="startRegister()">
          <i class="fas fa-plus-circle"></i>
          <span id="btnText">Register Document</span>
        </button>

        <!-- Status Display -->
        <div class="status-container">
          <div class="status-title">
            <i class="fas fa-info-circle"></i> Status
          </div>
          <div id="status" class="status-content">Ready to register documents. Please connect your wallet and select files.</div>
        </div>
      </div>
    </div>

    <script>
      // Application Configuration
      const APP_CONFIG = {
        REGISTER_FEE: "0.2", // 0.2 POL per document
        MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
        MIN_BDIO_ID_LENGTH: 10,
        MAX_BDIO_ID_LENGTH: 64,
        MAX_STRING_LENGTH: 256
      };

      const NETWORK_CONFIG = {
        chainId: "0x89", // Polygon Mainnet
        chainName: "Polygon Mainnet",
        rpcUrls: ["https://polygon-rpc.com/"],
        nativeCurrency: {
          name: "POL",
          symbol: "POL",
          decimals: 18
        },
        blockExplorerUrls: ["https://polygonscan.com/"]
      };

      // Contract address - will be loaded from server
      let CONTRACT_ADDRESS = null;

      // Load contract configuration from server
      async function loadContractConfig() {
        try {
          const response = await fetch('/api/config');
          if (response.ok) {
            const config = await response.json();
            CONTRACT_ADDRESS = config.address;
            console.log('✅ Contract loaded:', CONTRACT_ADDRESS);
            return true;
          } else {
            throw new Error('Server config not available');
          }
        } catch (error) {
          console.warn('⚠️ Using fallback contract address');
          CONTRACT_ADDRESS = '0xC954fD7aC0cAeaD6BF54e5d9d8123b8D5E87FE6f'; // Fallback from .env
          return false;
        }
      }

      // Contract ABI (simplified for registration functions)
      const CONTRACT_ABI = [
        "function register(string bdioId, string hashHex, string title, string category, string metadataUri) external payable",
        "function batchRegister(string[] bdioIds, string[] hashHexes, string[] titles, string[] categories, string[] metadataUris, uint256[] expiries) external payable",
        "function registerFee() external view returns (uint256)",
        "function exists(string bdioId) external view returns (bool)",
        "event DocumentRegistered(string indexed bdioId, address indexed owner)",
        "event DocumentBatchRegistered(uint count)"
      ];

      // Global variables
      let provider;
      let signer;
      let contract;
      let currentAccount;
      let currentMode = "single";

      // Initialize the application
      window.addEventListener('load', async () => {
        await initializeApp();
        setupEventListeners();
      });

      async function initializeApp() {
        try {
          // Load contract configuration first
          log("🚀 Loading contract configuration...");
          await loadContractConfig();

          if (!CONTRACT_ADDRESS) {
            log("❌ Contract address not available. Please check server configuration.");
            return;
          }

          log(`📋 Using contract: ${CONTRACT_ADDRESS}`);

          if (typeof window.ethereum !== 'undefined') {
            provider = new ethers.BrowserProvider(window.ethereum);
            await connectWallet();
          } else {
            log("❌ MetaMask not detected. Please install MetaMask to use this application.");
          }
        } catch (error) {
          console.error("Initialization error:", error);
          log("❌ Failed to initialize application: " + error.message);
        }
      }

      async function connectWallet() {
        try {
          const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
          if (accounts.length > 0) {
            currentAccount = accounts[0];
            signer = await provider.getSigner();
            contract = new ethers.Contract(CONTRACT_ADDRESS, CONTRACT_ABI, signer);

            // Update UI
            document.getElementById('walletAddress').textContent =
              currentAccount.substring(0, 6) + '...' + currentAccount.substring(38);
            document.getElementById('walletInfo').classList.add('show');

            // Update fee display
            try {
              const fee = await contract.registerFee();
              const feeInEther = ethers.formatEther(fee);
              document.getElementById('feeDisplay').textContent = `${feeInEther} POL per document`;
              log(`💰 Registration fee: ${feeInEther} POL per document`);
            } catch (e) {
              console.warn("Could not fetch fee from contract, using default");
              document.getElementById('feeDisplay').textContent = `${APP_CONFIG.REGISTER_FEE} POL per document (fallback)`;
              log(`💰 Using fallback fee: ${APP_CONFIG.REGISTER_FEE} POL per document`);
            }

            log("✅ Wallet connected successfully. Ready to register documents.");
          }
        } catch (error) {
          console.error("Wallet connection error:", error);
          log("❌ Failed to connect wallet: " + error.message);
        }
      }

      function setupEventListeners() {
        // Mode selection
        document.querySelectorAll('.mode-option').forEach(option => {
          option.addEventListener('click', function() {
            document.querySelectorAll('.mode-option').forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            currentMode = this.dataset.mode;
            updateFileInput();
            updateUI();
          });
        });

        // File input handling
        const fileInput = document.getElementById('file');
        const fileDisplay = document.getElementById('fileDisplayText');

        fileInput.addEventListener('change', function() {
          const files = this.files;
          if (files.length > 0) {
            if (files.length === 1) {
              fileDisplay.textContent = `Selected: ${files[0].name}`;
            } else {
              fileDisplay.textContent = `Selected: ${files.length} files`;
            }
          } else {
            fileDisplay.textContent = "Click to select file(s) or drag and drop";
          }
        });

        // Drag and drop
        const fileWrapper = document.querySelector('.file-input-display');
        fileWrapper.addEventListener('dragover', (e) => {
          e.preventDefault();
          fileWrapper.style.borderColor = '#667eea';
          fileWrapper.style.background = '#f0f4ff';
        });

        fileWrapper.addEventListener('dragleave', (e) => {
          e.preventDefault();
          fileWrapper.style.borderColor = '#e1e8ed';
          fileWrapper.style.background = '#f8f9fa';
        });

        fileWrapper.addEventListener('drop', (e) => {
          e.preventDefault();
          fileWrapper.style.borderColor = '#e1e8ed';
          fileWrapper.style.background = '#f8f9fa';

          const files = e.dataTransfer.files;
          if (files.length > 0) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
          }
        });
      }

      function updateFileInput() {
        const fileInput = document.getElementById('file');
        const fileDisplay = document.getElementById('fileDisplayText');

        if (currentMode === "multiple") {
          fileInput.setAttribute("multiple", "");
          fileDisplay.textContent = "Click to select multiple files or drag and drop";
        } else {
          fileInput.removeAttribute("multiple");
          fileDisplay.textContent = "Click to select file or drag and drop";
        }

        fileInput.value = "";
        fileDisplay.textContent = fileDisplay.textContent;
      }

      function updateUI() {
        const btnText = document.getElementById('btnText');
        if (currentMode === "multiple") {
          btnText.innerHTML = '<i class="fas fa-plus-circle"></i> Batch Register Documents';
        } else {
          btnText.innerHTML = '<i class="fas fa-plus-circle"></i> Register Document';
        }
      }

      async function startRegister() {
        if (!contract) {
          log("❌ Please connect your wallet first.");
          return;
        }

        if (currentMode === "multiple") {
          await batchRegister();
        } else {
          await register();
        }
      }

      function log(msg, type = 'info') {
        const statusElement = document.getElementById("status");
        statusElement.textContent = msg;

        // Remove existing status classes
        statusElement.classList.remove('error', 'success');

        // Add appropriate class based on type
        if (type === 'error') {
          statusElement.classList.add('error');
        } else if (type === 'success') {
          statusElement.classList.add('success');
        }
      }

      function setLoading(isLoading) {
        const btn = document.getElementById("btnRegister");
        const btnText = document.getElementById("btnText");

        if (isLoading) {
          btn.disabled = true;
          btnText.innerHTML = '<div class="loading"></div> Processing...';
        } else {
          btn.disabled = false;
          updateUI();
        }
      }

      async function validateInputs() {
        const title = document.getElementById("title").value.trim();
        const fileInput = document.getElementById("file");

        if (!title) {
          log("❌ Document title is required.", 'error');
          return false;
        }

        if (title.length > 256) {
          log("❌ Title is too long (max 256 characters).", 'error');
          return false;
        }

        if (fileInput.files.length === 0) {
          log("❌ Please select at least one file.", 'error');
          return false;
        }

        // Validate file sizes (max 10MB per file)
        for (let file of fileInput.files) {
          if (file.size > 10 * 1024 * 1024) {
            log(`❌ File "${file.name}" is too large (max 10MB per file).`, 'error');
            return false;
          }
        }

        return true;
      }

      async function generateBdioId(hashHex) {
        // BDIO ID is the last 10 characters of the hash (as per FORMAT BDIO ID.txt)
        return hashHex.slice(-10);
      }

      async function hashFile(file) {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = async function(e) {
            try {
              const arrayBuffer = e.target.result;
              const hashHex = ethers.keccak256(new Uint8Array(arrayBuffer));
              resolve(hashHex);
            } catch (error) {
              reject(error);
            }
          };
          reader.onerror = reject;
          reader.readAsArrayBuffer(file);
        });
      }

      async function register() {
        try {
          if (!await validateInputs()) return;

          setLoading(true);
          log("🔍 Processing document...");

          const title = document.getElementById("title").value.trim();
          const category = document.getElementById("category").value.trim() || "";
          const metadataUri = document.getElementById("metadataUri").value.trim() || "";
          const fileInput = document.getElementById("file");
          const file = fileInput.files[0];

          // Hash the file
          log("🔐 Generating document hash...");
          const hashHex = await hashFile(file);
          const bdioId = await generateBdioId(hashHex);

          // Check if document already exists
          log("🔍 Checking document uniqueness...");
          const exists = await contract.exists(bdioId);
          if (exists) {
            log(`❌ Document with BDIO ID "${bdioId}" already exists.`, 'error');
            return;
          }

          // Get current registration fee
          const currentFee = await contract.registerFee();

          log("📡 Submitting registration to blockchain...");
          const tx = await contract.register(
            bdioId,
            hashHex,
            title,
            category,
            metadataUri,
            { value: currentFee }
          );

          log("⏳ Waiting for blockchain confirmation...");
          const receipt = await tx.wait();

          const successMsg = `✅ Document registered successfully!

📄 Document: ${title}
🆔 BDIO ID: ${bdioId}
🔗 Transaction: ${receipt.hash}
⛽ Gas Used: ${receipt.gasUsed.toString()}
🌐 View on Explorer: https://polygonscan.com/tx/${receipt.hash}`;

          log(successMsg, 'success');

          // Clear form
          document.getElementById("title").value = "";
          document.getElementById("category").value = "";
          fileInput.value = "";
          document.getElementById("fileDisplayText").textContent = "Click to select file or drag and drop";

        } catch (error) {
          console.error("Registration error:", error);
          let errorMsg = "❌ Registration failed: ";

          if (error.code === 'INSUFFICIENT_FUNDS') {
            errorMsg += "Insufficient funds for transaction.";
          } else if (error.code === 'USER_REJECTED') {
            errorMsg += "Transaction was rejected by user.";
          } else if (error.message.includes('Already exists')) {
            errorMsg += "Document already exists in the registry.";
          } else if (error.message.includes('Duplicate document hash')) {
            errorMsg += "A document with this content already exists.";
          } else if (error.message.includes('Rate limit exceeded')) {
            errorMsg += "Please wait before registering another document.";
          } else {
            errorMsg += error.message || "Unknown error occurred.";
          }

          log(errorMsg, 'error');
        } finally {
          setLoading(false);
        }
      }

      async function batchRegister() {
        try {
          if (!await validateInputs()) return;

          setLoading(true);
          log("🔍 Processing multiple documents...");

          const title = document.getElementById("title").value.trim();
          const category = document.getElementById("category").value.trim() || "";
          const metadataUri = document.getElementById("metadataUri").value.trim() || "";
          const fileInput = document.getElementById("file");
          const files = Array.from(fileInput.files);

          if (files.length > 50) {
            log("❌ Maximum 50 files allowed per batch.", 'error');
            return;
          }

          const bdioIds = [];
          const hashHexes = [];
          const titles = [];
          const categories = [];
          const metadataUris = [];
          const expiries = [];

          log(`� Generating hashes for ${files.length} files...`);

          for (let i = 0; i < files.length; i++) {
            const file = files[i];
            log(`🔐 Processing file ${i + 1}/${files.length}: ${file.name}`);

            const hashHex = await hashFile(file);
            const bdioId = await generateBdioId(hashHex);

            // Check if document already exists
            const exists = await contract.exists(bdioId);
            if (exists) {
              log(`❌ File "${file.name}" already exists with BDIO ID "${bdioId}".`, 'error');
              return;
            }

            bdioIds.push(bdioId);
            hashHexes.push(hashHex);
            titles.push(title);
            categories.push(category);
            metadataUris.push(metadataUri);
            expiries.push(0); // No expiry
          }

          // Get current registration fee
          const currentFee = await contract.registerFee();
          const totalFee = currentFee * BigInt(files.length);

          log(`📡 Submitting batch registration for ${files.length} documents...`);
          const tx = await contract.batchRegister(
            bdioIds,
            hashHexes,
            titles,
            categories,
            metadataUris,
            expiries,
            { value: totalFee }
          );

          log("⏳ Waiting for blockchain confirmation...");
          const receipt = await tx.wait();

          const successMsg = `✅ Batch registration successful!

📄 Documents: ${files.length}
🆔 BDIO IDs: ${bdioIds.join(', ')}
🔗 Transaction: ${receipt.hash}
⛽ Gas Used: ${receipt.gasUsed.toString()}
💰 Total Fee: ${ethers.formatEther(totalFee)} POL
🌐 View on Explorer: https://polygonscan.com/tx/${receipt.hash}`;

          log(successMsg, 'success');

          // Clear form
          document.getElementById("title").value = "";
          document.getElementById("category").value = "";
          fileInput.value = "";
          document.getElementById("fileDisplayText").textContent = "Click to select multiple files or drag and drop";

        } catch (error) {
          console.error("Batch registration error:", error);
          let errorMsg = "❌ Batch registration failed: ";

          if (error.code === 'INSUFFICIENT_FUNDS') {
            errorMsg += "Insufficient funds for transaction.";
          } else if (error.code === 'USER_REJECTED') {
            errorMsg += "Transaction was rejected by user.";
          } else if (error.message.includes('Invalid batch size')) {
            errorMsg += "Invalid number of documents (max 50 per batch).";
          } else if (error.message.includes('Mismatched array lengths')) {
            errorMsg += "Internal error: mismatched data arrays.";
          } else if (error.message.includes('Rate limit exceeded')) {
            errorMsg += "Please wait before registering more documents.";
          } else {
            errorMsg += error.message || "Unknown error occurred.";
          }

          log(errorMsg, 'error');
        } finally {
          setLoading(false);
        }
      }
    </script>
  </body>
</html>