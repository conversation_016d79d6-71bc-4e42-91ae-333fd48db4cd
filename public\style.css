/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  min-height: 100vh;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header styles */
.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 300;
}

/* Search section */
.search-section {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.search-container {
  display: flex;
  gap: 15px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  max-width: 400px;
  padding: 15px 20px;
  font-size: 1.1rem;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-btn {
  padding: 15px 30px;
  font-size: 1.1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 10px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  min-width: 160px;
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.search-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Loading spinner */
.loading-spinner {
  text-align: center;
  padding: 20px;
  color: #667eea;
  font-size: 1.1rem;
}

.loading-spinner i {
  margin-right: 10px;
  font-size: 1.2rem;
}

/* Document info container */
.document-info {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Status banner */
.status-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.status-item.expired {
  color: #ff6b6b;
}

.status-item.valid {
  color: #51cf66;
}

/* Info grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 0;
}

/* Info cards */
.info-card {
  padding: 30px;
  border-bottom: 1px solid #e9ecef;
}

.info-card:nth-child(even) {
  background: #f8f9fa;
}

.info-card h3 {
  color: #495057;
  margin-bottom: 20px;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-card h3 i {
  color: #667eea;
}

.info-row {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;
}

.label {
  font-weight: 600;
  color: #6c757d;
  min-width: 120px;
  margin-right: 15px;
}

.value {
  flex: 1;
  word-break: break-all;
}

.value.address {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.value.hash {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.external-link {
  color: #667eea;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: color 0.3s ease;
}

.external-link:hover {
  color: #764ba2;
  text-decoration: underline;
}

/* Section cards */
.section-card {
  padding: 30px;
  border-bottom: 1px solid #e9ecef;
}

.section-card:last-child {
  border-bottom: none;
}

.section-card h3 {
  color: #495057;
  margin-bottom: 20px;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-card h3 i {
  color: #667eea;
}

/* Version items */
.versions-list, .history-list, .endorsements-list, .signers-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.version-item, .history-item, .endorsement-item, .signer-item {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  border-left: 4px solid #667eea;
  transition: transform 0.2s ease;
}

.version-item:hover, .history-item:hover, .endorsement-item:hover {
  transform: translateX(5px);
}

.version-header, .history-header, .endorsement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.version-number, .history-index, .endorsement-index {
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.version-date, .history-date, .endorsement-date {
  color: #6c757d;
  font-size: 0.9rem;
}

.version-details, .history-details, .endorsement-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.version-hash, .version-editor, .history-owner, .endorsement-signer {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  background: white;
  padding: 8px 12px;
  border-radius: 6px;
}

.version-note, .history-note, .endorsement-note {
  color: #6c757d;
  font-style: italic;
}

/* Signer items */
.signer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.signer-address {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.signer-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.signer-status.signed {
  background: #d4edda;
  color: #155724;
}

.signer-status.pending {
  background: #fff3cd;
  color: #856404;
}

.signer-status.revoked {
  background: #f8d7da;
  color: #721c24;
}

.signer-status.error {
  background: #f8d7da;
  color: #721c24;
}

/* Expiry management */
.expiry-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.expiry-update {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
}

.input-group {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.datetime-input {
  flex: 1;
  min-width: 200px;
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.datetime-input:focus {
  outline: none;
  border-color: #667eea;
}

.btn-primary {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Empty and error states */
.empty-state, .error-state, .loading-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  font-style: italic;
}

.error-state {
  color: #dc3545;
}

.loading-placeholder {
  color: #667eea;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .search-section {
    padding: 20px;
  }

  .search-container {
    flex-direction: column;
  }

  .search-input {
    max-width: 100%;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .info-card, .section-card {
    padding: 20px;
  }

  .status-banner {
    padding: 15px 20px;
    flex-direction: column;
    align-items: flex-start;
  }

  .input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .datetime-input {
    min-width: auto;
  }

  .signer-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .version-header, .history-header, .endorsement-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .label {
    min-width: auto;
    margin-bottom: 5px;
  }

  .info-row {
    flex-direction: column;
  }
}

/* Animation for loading spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
