/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  min-height: 100vh;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header styles */
.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 300;
}

/* Search section */
.search-section {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.search-container {
  display: flex;
  gap: 15px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  max-width: 400px;
  padding: 15px 20px;
  font-size: 1.1rem;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-btn {
  padding: 15px 30px;
  font-size: 1.1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 10px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  min-width: 160px;
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.search-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Loading spinner */
.loading-spinner {
  text-align: center;
  padding: 20px;
  color: #667eea;
  font-size: 1.1rem;
}

.loading-spinner i {
  margin-right: 10px;
  font-size: 1.2rem;
}

/* Document info container */
.document-info {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Status banner */
.status-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.status-item.expired {
  color: #ff6b6b;
}

.status-item.valid {
  color: #51cf66;
}

/* Info grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 0;
}

/* Info cards */
.info-card {
  padding: 30px;
  border-bottom: 1px solid #e9ecef;
}

.info-card:nth-child(even) {
  background: #f8f9fa;
}

.info-card h3 {
  color: #495057;
  margin-bottom: 20px;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-card h3 i {
  color: #667eea;
}

.info-row {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;
}

.label {
  font-weight: 600;
  color: #6c757d;
  min-width: 120px;
  margin-right: 15px;
}

.value {
  flex: 1;
  word-break: break-all;
}

.value.address {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.value.hash {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.external-link {
  color: #667eea;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: color 0.3s ease;
}

.external-link:hover {
  color: #764ba2;
  text-decoration: underline;
}

/* Section cards */
.section-card {
  padding: 30px;
  border-bottom: 1px solid #e9ecef;
}

.section-card:last-child {
  border-bottom: none;
}

.section-card h3 {
  color: #495057;
  margin-bottom: 20px;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-card h3 i {
  color: #667eea;
}

/* Version items */
.versions-list, .history-list, .endorsements-list, .signers-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.version-item, .history-item, .endorsement-item, .signer-item {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  border-left: 4px solid #667eea;
  transition: transform 0.2s ease;
}

.version-item:hover, .history-item:hover, .endorsement-item:hover {
  transform: translateX(5px);
}

.version-header, .history-header, .endorsement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.version-number, .history-index, .endorsement-index {
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.version-date, .history-date, .endorsement-date {
  color: #6c757d;
  font-size: 0.9rem;
}

.version-details, .history-details, .endorsement-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.version-hash, .version-editor, .history-owner, .endorsement-signer {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  background: white;
  padding: 8px 12px;
  border-radius: 6px;
}

.version-note, .history-note, .endorsement-note {
  color: #6c757d;
  font-style: italic;
}

/* Signer items */
.signer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.signer-address {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.signer-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.signer-status.signed {
  background: #d4edda;
  color: #155724;
}

.signer-status.pending {
  background: #fff3cd;
  color: #856404;
}

.signer-status.revoked {
  background: #f8d7da;
  color: #721c24;
}

.signer-status.error {
  background: #f8d7da;
  color: #721c24;
}

/* Expiry management */
.expiry-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.expiry-update {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
}

.input-group {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.datetime-input {
  flex: 1;
  min-width: 200px;
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.datetime-input:focus {
  outline: none;
  border-color: #667eea;
}

.btn-primary {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Empty and error states */
.empty-state, .error-state, .loading-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  font-style: italic;
}

.error-state {
  color: #dc3545;
}

.loading-placeholder {
  color: #667eea;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .search-section {
    padding: 20px;
  }

  .search-container {
    flex-direction: column;
  }

  .search-input {
    max-width: 100%;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .info-card, .section-card {
    padding: 20px;
  }

  .status-banner {
    padding: 15px 20px;
    flex-direction: column;
    align-items: flex-start;
  }

  .input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .datetime-input {
    min-width: auto;
  }

  .signer-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .version-header, .history-header, .endorsement-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .label {
    min-width: auto;
    margin-bottom: 5px;
  }

  .info-row {
    flex-direction: column;
  }
}

/* Animation for loading spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ========== ADDITIONAL COMPONENTS FOR ALL PAGES ========== */

/* Glass effect components */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Dashboard specific styles */
.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  text-align: center;
  position: relative;
}

.dashboard-header h1 {
  font-size: 3rem;
  margin-bottom: 15px;
  font-weight: 300;
}

.dashboard-header p {
  opacity: 0.9;
  font-size: 1.2rem;
  margin-bottom: 30px;
}

.header-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

/* Main content area */
.main-content {
  padding: 40px;
}

/* Wallet section */
.wallet-section {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
  border: 1px solid #4caf50;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 40px;
  display: none;
}

.wallet-section.show {
  display: block;
}

.wallet-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.wallet-info h3 {
  color: #2e7d32;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.wallet-address {
  font-family: 'Courier New', monospace;
  background: rgba(255, 255, 255, 0.7);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.9rem;
  word-break: break-all;
}

/* Statistics grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stat-card i {
  font-size: 2.5rem;
  color: #667eea;
  margin-bottom: 15px;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

/* Button styles */
.btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.btn-outline {
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.btn-large {
  padding: 15px 30px;
  font-size: 1.1rem;
}

.btn-small {
  padding: 8px 16px;
  font-size: 0.9rem;
}

/* Form styles */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #495057;
}

.form-input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error {
  border-color: #dc3545;
  background: #fff5f5;
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

/* File upload styles */
.file-upload {
  position: relative;
  display: inline-block;
  width: 100%;
}

.file-upload-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-upload-label {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border: 2px dashed #667eea;
  border-radius: 8px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-upload-label:hover {
  background: #e9ecef;
  border-color: #764ba2;
}

.file-upload-label i {
  margin-right: 10px;
  font-size: 1.2rem;
  color: #667eea;
}

/* Alert styles */
.alert {
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.alert-success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.alert-error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.alert-warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
}

.alert-info {
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
}

/* Table styles */
.table-container {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  margin-bottom: 30px;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px;
  text-align: left;
  font-weight: 600;
}

.table td {
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
}

.table tr:hover {
  background: #f8f9fa;
}

.table tr:last-child td {
  border-bottom: none;
}

/* Action grid for cards */
.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.action-card {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.action-card h3 {
  color: #495057;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.action-card h3 i {
  color: #667eea;
  font-size: 1.5rem;
}

.action-card p {
  color: #6c757d;
  margin-bottom: 20px;
  line-height: 1.6;
}

/* Section styles */
.section {
  margin-bottom: 40px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.section-title {
  color: #495057;
  font-size: 1.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-title i {
  color: #667eea;
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.status-active {
  background: #d4edda;
  color: #155724;
}

.status-inactive {
  background: #f8d7da;
  color: #721c24;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-archived {
  background: #e2e3e5;
  color: #495057;
}

/* Connection status */
.connection-status {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  margin-bottom: 20px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-connected {
  background-color: #10b981;
}

.status-disconnected {
  background-color: #ef4444;
}

.status-loading {
  background-color: #f59e0b;
  animation: pulse 2s infinite;
}

/* Fee information */
.fee-info {
  background: #fff3cd;
  border: 1px solid #ffc107;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.fee-info i {
  color: #856404;
}

/* Log container */
.log-container {
  background: #1a1a1a;
  border-radius: 8px;
  padding: 20px;
  height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #00ff00;
  margin-bottom: 20px;
}

.log-container::-webkit-scrollbar {
  width: 8px;
}

.log-container::-webkit-scrollbar-track {
  background: #2a2a2a;
}

.log-container::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

.hidden { display: none; }
.block { display: block; }
.flex { display: flex; }
.grid { display: grid; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.font-mono { font-family: 'Courier New', monospace; }
.font-bold { font-weight: bold; }
.font-normal { font-weight: normal; }

.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }
