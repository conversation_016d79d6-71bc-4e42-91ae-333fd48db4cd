<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>🔍 Verify Document - BDIO Registry</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<link rel="stylesheet" href="style.css">
<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
  }

  .container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    text-align: center;
  }

  .header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 300;
  }

  .header p {
    opacity: 0.9;
    font-size: 1.1rem;
  }

  .form-container {
    padding: 60px 40px;
  }

  .drop-zone {
    border: 3px dashed #667eea;
    border-radius: 20px;
    padding: 60px 40px;
    text-align: center;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 30px;
    position: relative;
  }

  .drop-zone:hover {
    border-color: #5a67d8;
    background: linear-gradient(135deg, #f0f4ff 0%, #e6edff 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
  }

  .drop-zone:active {
    transform: translateY(0);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
  }

  .drop-zone.dragover {
    border-color: #4c51bf;
    background: linear-gradient(135deg, #e6edff 0%, #ddd6fe 100%);
    transform: scale(1.02);
  }

  .drop-zone-content {
    /* Remove pointer-events: none to allow clicking */
  }

  .drop-zone-icon {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 20px;
  }

  .drop-zone h3 {
    font-size: 1.5rem;
    color: #4a5568;
    margin-bottom: 10px;
  }

  .drop-zone p {
    color: #718096;
    font-size: 1rem;
    margin-bottom: 20px;
  }

  .file-types {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
  }

  .file-type {
    background: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    color: #667eea;
    border: 1px solid #e2e8f0;
  }

  .file-input {
    display: none;
  }

  .file-preview {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    display: none;
  }

  .file-info {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .file-icon {
    font-size: 2rem;
    color: #667eea;
  }

  .file-details h4 {
    color: #2d3748;
    margin-bottom: 5px;
  }

  .file-details p {
    color: #718096;
    font-size: 0.9rem;
  }

  .remove-file {
    margin-left: auto;
    background: #fed7d7;
    color: #e53e3e;
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .remove-file:hover {
    background: #feb2b2;
  }

  .verify-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 18px 40px;
    border-radius: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
  }

  .verify-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
  }

  .verify-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .status-container {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    border-left: 4px solid #667eea;
    display: none;
  }

  .status-title {
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
  }

  .status-content {
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #666;
    line-height: 1.5;
    max-height: 200px;
    overflow-y: auto;
  }

  /* Modal Styles */
  .modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
  }

  .modal-content {
    background: white;
    margin: 10% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
  }

  @keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  .modal-header {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    color: white;
    padding: 20px;
    border-radius: 20px 20px 0 0;
    text-align: center;
  }

  .modal-body {
    padding: 30px;
    text-align: center;
  }

  .modal-body h3 {
    color: #2d3748;
    margin-bottom: 15px;
  }

  .modal-body p {
    color: #718096;
    line-height: 1.6;
    margin-bottom: 25px;
  }

  .modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
  }

  .btn-register {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .btn-register:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }

  .btn-close {
    background: #e2e8f0;
    color: #4a5568;
    border: none;
    padding: 12px 25px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .btn-close:hover {
    background: #cbd5e0;
  }

  @media (max-width: 768px) {
    .container {
      margin: 10px;
      border-radius: 15px;
    }

    .form-container {
      padding: 30px 20px;
    }

    .header {
      padding: 30px 20px;
    }

    .header h1 {
      font-size: 2rem;
    }

    .drop-zone {
      padding: 40px 20px;
    }

    .modal-content {
      margin: 20% auto;
      width: 95%;
    }
  }
</style>
</head>
<body>
<div class="container">
  <div class="header">
    <h1><i class="fas fa-shield-alt"></i> Document Verification</h1>
    <p>Verify document authenticity and registration status</p>
  </div>

  <div class="form-container">
    <!-- Drop Zone -->
    <div class="drop-zone" id="dropZone">
      <div class="drop-zone-content">
        <div class="drop-zone-icon">
          <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <h3>Drop your document here</h3>
        <p>or click to browse files</p>
        <div class="file-types">
          <span class="file-type">PDF</span>
          <span class="file-type">DOC</span>
          <span class="file-type">DOCX</span>
          <span class="file-type">TXT</span>
        </div>
      </div>
    </div>

    <input type="file" id="fileInput" class="file-input" accept=".pdf,.doc,.docx,.txt">

    <!-- File Preview -->
    <div class="file-preview" id="filePreview">
      <div class="file-info">
        <div class="file-icon">
          <i class="fas fa-file-alt"></i>
        </div>
        <div class="file-details">
          <h4 id="fileName">Document.pdf</h4>
          <p id="fileSize">2.5 MB</p>
        </div>
        <button class="remove-file" onclick="removeFile()">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- Test Button for Debugging -->
    <button onclick="testClick()" style="background: #e53e3e; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-bottom: 10px; cursor: pointer;">
      🧪 Test File Dialog
    </button>

    <!-- Manual Debug Button -->
    <button onclick="document.getElementById('fileInput').click(); alert('Manual click triggered!');" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-bottom: 10px; cursor: pointer;">
      🔧 Manual File Input Click
    </button>

    <!-- Debug Info Button -->
    <button onclick="console.log('Drop zone:', document.getElementById('dropZone')); console.log('File input:', document.getElementById('fileInput')); alert('Check console for debug info');" style="background: #17a2b8; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-bottom: 10px; cursor: pointer;">
      🔍 Debug Info
    </button>

    <!-- Test Drag Events Button -->
    <button onclick="
      const dz = document.getElementById('dropZone');
      console.log('Testing drag events...');
      console.log('ondragover:', dz.ondragover);
      console.log('ondrop:', dz.ondrop);
      console.log('ondragenter:', dz.ondragenter);
      alert('Check console for drag event handlers');
    " style="background: #6f42c1; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-bottom: 10px; cursor: pointer;">
      🧪 Test Drag Events
    </button>

    <!-- Force Setup Button -->
    <button onclick="
      console.log('🔧 FORCE SETUP TRIGGERED!');
      setupDragAndDrop();
      alert('Setup forced! Check console and try drag events test again.');
    " style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-bottom: 10px; cursor: pointer;">
      🔧 Force Setup Now
    </button>

    <!-- Verify Button -->
    <button class="verify-btn" id="verifyBtn" onclick="verifyDocument()" disabled>
      <i class="fas fa-search"></i>
      Verify Document Now
    </button>

    <!-- Status Container -->
    <div class="status-container" id="statusContainer">
      <div class="status-title">
        <i class="fas fa-info-circle"></i> Verification Status
      </div>
      <div id="status" class="status-content"></div>
    </div>
  </div>
</div>

<!-- Not Registered Modal -->
<div id="notRegisteredModal" class="modal">
  <div class="modal-content">
    <div class="modal-header">
      <h2><i class="fas fa-exclamation-triangle"></i> Document Not Registered</h2>
    </div>
    <div class="modal-body">
      <h3>This document is not yet registered</h3>
      <p>The document you uploaded has not been registered in the BDIO Registry. Would you like to register it now?</p>
      <div class="modal-buttons">
        <button class="btn-register" onclick="goToRegister()">
          <i class="fas fa-plus"></i> Register Now
        </button>
        <button class="btn-close" onclick="closeModal()">
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// IMMEDIATE TEST - Define functions right away
console.log('🚀 Script starting...');

// Test function that should work immediately
function testClick() {
  alert('Test button clicked! This proves JavaScript is working.');
  const fileInput = document.getElementById('fileInput');
  console.log('File input found:', fileInput);
  if (fileInput) {
    fileInput.click();
  }
}

// Make it globally available immediately
window.testClick = testClick;

// Application Configuration
const APP_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'],
  ALLOWED_EXTENSIONS: ['.pdf', '.doc', '.docx', '.txt']
};

// Contract addresses - will be loaded from server
let bdioCoreAddress = null;

// Load contract configuration
async function loadContractConfig() {
  try {
    const response = await fetch('/api/config');
    if (response.ok) {
      const config = await response.json();
      bdioCoreAddress = config.address;
      console.log('✅ Contract configuration loaded');
      return true;
    } else {
      throw new Error('Server config not available');
    }
  } catch (error) {
    console.warn('⚠️ Using fallback contract addresses');
    bdioCoreAddress = '0xC954fD7aC0cAeaD6BF54e5d9d8123b8D5E87FE6f';
    return false;
  }
}

// Complete ABI for the functions we need
const bdioAbi = [
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "verifyDocument",
    "outputs": [
      {"internalType": "address", "name": "owner", "type": "address"},
      {"internalType": "bool", "name": "active", "type": "bool"},
      {"internalType": "bool", "name": "archived", "type": "bool"},
      {"internalType": "uint256", "name": "timestamp", "type": "uint256"},
      {"internalType": "uint256", "name": "versionCount", "type": "uint256"},
      {"internalType": "string", "name": "category", "type": "string"},
      {"internalType": "string", "name": "metadataUri", "type": "string"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}],
    "name": "exists",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "view",
    "type": "function"
  }
];

let provider, contract;
let selectedFile = null;

// Initialize the application
async function init() {
  console.log('🚀 Initializing Document Verification...');

  // Setup drag and drop FIRST
  setupDragAndDrop();

  // Load contract configuration
  await loadContractConfig();

  if (!bdioCoreAddress) {
    console.log('❌ Contract address not available. Please check server configuration.');
    return;
  }

  // Setup contract (read-only, no wallet required)
  try {
    provider = new ethers.JsonRpcProvider('https://polygon-amoy.drpc.org');
    contract = new ethers.Contract(bdioCoreAddress, bdioAbi, provider);

    console.log(`✅ Connected to Polygon Amoy network`);
    console.log(`📋 BDIO Registry: ${bdioCoreAddress}`);

  } catch (error) {
    console.error('Connection error:', error);
    console.log(`❌ Connection failed: ${error.message}`);
  }
}

// FOCUSED setup function - since manual click works, focus on drag & drop
function setupDragAndDrop() {
  console.log('🔧 FOCUSED setup starting...');
  console.log('🔧 Current time:', new Date().toLocaleTimeString());

  const dropZone = document.getElementById('dropZone');
  const fileInput = document.getElementById('fileInput');

  console.log('🔧 Drop zone:', dropZone);
  console.log('🔧 File input:', fileInput);

  if (!dropZone) {
    console.error('❌ DROP ZONE NOT FOUND!');
    console.error('❌ Available elements with IDs:');
    const allElements = document.querySelectorAll('[id]');
    allElements.forEach(el => console.log('  -', el.id, el.tagName));
    return;
  }

  if (!fileInput) {
    console.error('❌ FILE INPUT NOT FOUND!');
    return;
  }

  console.log('✅ Both elements found!');
  console.log('🔧 Drop zone ID:', dropZone.id);
  console.log('🔧 Drop zone class:', dropZone.className);
  console.log('🔧 File input ID:', fileInput.id);

  // Skip click handler since manual works - focus on drag & drop

  // File change handler
  fileInput.onchange = function(e) {
    console.log('📁 FILE SELECTED!', e.target.files.length);
    if (e.target.files.length > 0) {
      console.log('📁 File name:', e.target.files[0].name);
      console.log('📁 File size:', e.target.files[0].size);
      handleFile(e.target.files[0]);
    }
  };

  // Test if element can receive events
  console.log('🧪 Testing event attachment...');

  // Method 1: Direct property assignment
  console.log('🔧 Assigning ondragenter...');
  dropZone.ondragenter = function(e) {
    e.preventDefault();
    e.stopPropagation();
    console.log('📂 DRAG ENTER!');
    dropZone.style.backgroundColor = '#e6edff';
    dropZone.style.border = '3px solid #4c51bf';
  };
  console.log('🔧 ondragenter assigned:', typeof dropZone.ondragenter);

  console.log('🔧 Assigning ondragover...');
  dropZone.ondragover = function(e) {
    e.preventDefault();
    e.stopPropagation();
    console.log('📂 DRAG OVER!');
    dropZone.style.backgroundColor = '#e6edff';
    dropZone.style.border = '3px solid #4c51bf';
  };
  console.log('🔧 ondragover assigned:', typeof dropZone.ondragover);

  console.log('🔧 Assigning ondragleave...');
  dropZone.ondragleave = function(e) {
    e.preventDefault();
    e.stopPropagation();
    console.log('📂 DRAG LEAVE!');
    dropZone.style.backgroundColor = '';
    dropZone.style.border = '3px dashed #667eea';
  };
  console.log('🔧 ondragleave assigned:', typeof dropZone.ondragleave);

  console.log('🔧 Assigning ondrop...');
  dropZone.ondrop = function(e) {
    e.preventDefault();
    e.stopPropagation();
    console.log('📂 FILE DROPPED!');
    console.log('📂 DataTransfer:', e.dataTransfer);
    console.log('📂 Files:', e.dataTransfer.files);
    console.log('📂 Files length:', e.dataTransfer.files.length);

    // Reset styles
    dropZone.style.backgroundColor = '';
    dropZone.style.border = '3px dashed #667eea';

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      console.log('📂 Processing dropped file:', file.name);
      alert('File dropped: ' + file.name);
      handleFile(file);
    } else {
      console.error('❌ No files in drop event!');
      alert('No files detected in drop event!');
    }
  };
  console.log('🔧 ondrop assigned:', typeof dropZone.ondrop);

  // Method 2: Also try addEventListener as backup
  console.log('🧪 Adding addEventListener backups...');

  dropZone.addEventListener('dragenter', function(e) {
    e.preventDefault();
    console.log('📂 DRAG ENTER (addEventListener)!');
  });

  dropZone.addEventListener('dragover', function(e) {
    e.preventDefault();
    console.log('📂 DRAG OVER (addEventListener)!');
  });

  dropZone.addEventListener('drop', function(e) {
    e.preventDefault();
    console.log('📂 DROP (addEventListener)!');
    if (e.dataTransfer.files.length > 0) {
      console.log('📂 Files found via addEventListener');
    }
  });

  // Test if events are working
  console.log('🧪 Testing mouse events...');
  dropZone.onmouseenter = function() {
    console.log('🖱️ Mouse entered drop zone');
  };

  dropZone.onmouseleave = function() {
    console.log('🖱️ Mouse left drop zone');
  };

  console.log('✅ FOCUSED SETUP COMPLETE!');

  // Final verification
  console.log('🔧 FINAL VERIFICATION:');
  console.log('🔧 ondragenter:', typeof dropZone.ondragenter, dropZone.ondragenter);
  console.log('🔧 ondragover:', typeof dropZone.ondragover, dropZone.ondragover);
  console.log('🔧 ondragleave:', typeof dropZone.ondragleave, dropZone.ondragleave);
  console.log('🔧 ondrop:', typeof dropZone.ondrop, dropZone.ondrop);

  console.log('✅ Try dragging a file over the drop zone now...');
}

// Functions moved to top of script for global access

// Handle file selection
function handleFile(file) {
  // Validate file type
  if (!APP_CONFIG.ALLOWED_TYPES.includes(file.type)) {
    const extension = '.' + file.name.split('.').pop().toLowerCase();
    if (!APP_CONFIG.ALLOWED_EXTENSIONS.includes(extension)) {
      log('❌ Invalid file type. Please upload PDF, DOC, DOCX, or TXT files.');
      return;
    }
  }

  // Validate file size
  if (file.size > APP_CONFIG.MAX_FILE_SIZE) {
    log(`❌ File too large. Maximum size is ${APP_CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB.`);
    return;
  }

  selectedFile = file;
  showFilePreview(file);
  document.getElementById('verifyBtn').disabled = false;

  log(`✅ File selected: ${file.name} (${formatFileSize(file.size)})`);
}

// Show file preview
function showFilePreview(file) {
  const preview = document.getElementById('filePreview');
  const fileName = document.getElementById('fileName');
  const fileSize = document.getElementById('fileSize');
  const fileIcon = document.querySelector('.file-icon i');

  fileName.textContent = file.name;
  fileSize.textContent = formatFileSize(file.size);

  // Set appropriate icon based on file type
  if (file.type.includes('pdf')) {
    fileIcon.className = 'fas fa-file-pdf';
  } else if (file.type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) {
    fileIcon.className = 'fas fa-file-word';
  } else if (file.type.includes('text')) {
    fileIcon.className = 'fas fa-file-alt';
  } else {
    fileIcon.className = 'fas fa-file';
  }

  preview.style.display = 'block';
}

// Remove selected file
function removeFile() {
  selectedFile = null;
  document.getElementById('filePreview').style.display = 'none';
  document.getElementById('fileInput').value = '';
  document.getElementById('verifyBtn').disabled = true;
  hideStatus();

  log('📄 File removed');
}

// Format file size
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Verify document
async function verifyDocument() {
  if (!selectedFile) {
    log('❌ Please select a file first.');
    return;
  }

  if (!contract) {
    log('❌ Contract not initialized. Please refresh the page.');
    return;
  }

  try {
    showStatus();
    log('🔍 Starting document verification...');
    log(`📄 File: ${selectedFile.name}`);

    // Calculate file hash
    log('🔐 Calculating document hash...');
    const fileHash = await calculateFileHash(selectedFile);
    log(`🔐 Document hash: ${fileHash}`);

    // Generate BDIO ID from hash
    const bdioId = generateBDIOId(fileHash);
    log(`🆔 BDIO ID: ${bdioId}`);

    // Check if document exists in registry
    log('🔍 Checking registry...');
    const exists = await contract.exists(bdioId);

    if (exists) {
      log('✅ Document found in registry!');

      // Get document details
      const docInfo = await contract.verifyDocument(bdioId);
      const owner = docInfo[0];
      const active = docInfo[1];
      const archived = docInfo[2];
      const timestamp = docInfo[3];
      const category = docInfo[5];

      const registrationDate = new Date(Number(timestamp) * 1000);

      log(`📊 Document Status: ${active ? 'Active' : 'Inactive'}, ${archived ? 'Archived' : 'Not archived'}`);
      log(`👤 Owner: ${owner.slice(0, 6)}...${owner.slice(-4)}`);
      log(`📅 Registered: ${registrationDate.toLocaleString()}`);
      log(`📂 Category: ${category || 'Not specified'}`);

      log('🔄 Redirecting to document details...');

      // Redirect to get.html with BDIO ID
      setTimeout(() => {
        window.location.href = `get.html?bdioId=${bdioId}`;
      }, 2000);

    } else {
      log('❌ Document not found in registry');
      showNotRegisteredModal();
    }

  } catch (error) {
    console.error('Verification error:', error);
    log(`❌ Verification failed: ${error.message}`);
  }
}

// Calculate file hash using keccak256 (same as registration system)
async function calculateFileHash(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const arrayBuffer = e.target.result;
        const hashHex = ethers.keccak256(new Uint8Array(arrayBuffer));
        resolve(hashHex);
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsArrayBuffer(file);
  });
}

// Generate BDIO ID from hash (same as registration system)
function generateBDIOId(hashHex) {
  // BDIO ID is the last 10 characters of the hash (as per FORMAT BDIO ID.txt)
  return hashHex.slice(-10);
}

// Show not registered modal
function showNotRegisteredModal() {
  document.getElementById('notRegisteredModal').style.display = 'block';
}

// Close modal
function closeModal() {
  document.getElementById('notRegisteredModal').style.display = 'none';
}

// Go to register page
function goToRegister() {
  window.location.href = 'register.html';
}

// Show status container
function showStatus() {
  document.getElementById('statusContainer').style.display = 'block';
}

// Hide status container
function hideStatus() {
  document.getElementById('statusContainer').style.display = 'none';
  document.getElementById('status').textContent = '';
}

// Log function
function log(msg) {
  const el = document.getElementById('status');
  const timestamp = new Date().toLocaleTimeString();
  el.textContent += `[${timestamp}] ${msg}\n`;
  el.scrollTop = el.scrollHeight;
}

// Close modal when clicking outside
window.addEventListener('click', (e) => {
  const modal = document.getElementById('notRegisteredModal');
  if (e.target === modal) {
    closeModal();
  }
});

// VERY SIMPLE initialization
console.log('📜 Script loaded');

// Try multiple times to ensure it works
setTimeout(function() {
  console.log('🔧 First attempt...');
  setupDragAndDrop();
}, 500);

setTimeout(function() {
  console.log('🔧 Second attempt...');
  setupDragAndDrop();
}, 2000);

setTimeout(function() {
  console.log('🔧 Third attempt...');
  setupDragAndDrop();
}, 5000);

// Also try when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('🔧 DOM ready attempt...');
  setupDragAndDrop();
});

// And when window loads
window.addEventListener('load', function() {
  console.log('🔧 Window load attempt...');
  setupDragAndDrop();
  init();
});
